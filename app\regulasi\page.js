'use client'
import { useState, useEffect, useMemo, useCallback } from 'react'
import { Tab, TabGroup, TabList, TabPanel, TabPanels } from '@headlessui/react';
import { motion } from 'framer-motion';
import Nav from './../components/Nav'
import { useSearchParams } from 'next/navigation';
import { slideInUp, shouldReduceMotion } from './../components/AnimationConfig';

export default function RegulasiPage() {
  const [posts, setPosts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedIndex, setSelectedIndex] = useState(0);
  const prefersReducedMotion = shouldReduceMotion();
  const searchParams = useSearchParams();
  
  // Predefined categories
  const categories = useMemo(() => [
    { id: 'berkala', name: 'Informasi Berkala' },
    { id: 'setiap-saat', name: 'Informasi Setiap Saat' },
    { id: 'serta-merta', name: 'Informasi Serta Merta' },
    { id: 'publik', name: 'Informasi Publik' },
    { id: 'regulasi', name: 'Regulasi' }
  ], []);

  useEffect(() => {
    async function fetchData() {
      try {
        // Fetch posts
        const postsResponse = await fetch('/api/posts');
        if (!postsResponse.ok) {
          throw new Error('Failed to fetch posts');
        }
        const postsData = await postsResponse.json();
        
        // Filter published posts only
        const publishedPosts = postsData.filter(post => post.published);
        setPosts(publishedPosts);
        
        // Set selected tab based on URL parameter if available
        const tabParam = searchParams.get('tab');
        
        if (tabParam) {
          // Find index of tab that matches category id
          const categoryIndex = categories.findIndex(cat => 
            cat.id === tabParam || 
            cat.name.toLowerCase() === tabParam.toLowerCase()
          );
          
          if (categoryIndex !== -1) {
            setSelectedIndex(categoryIndex);
          }
        }
      } catch (error) {
        console.error('Error fetching data:', error);
        setError('Gagal memuat data');
      } finally {
        setLoading(false);
      }
    }    fetchData();
  }, [searchParams, categories]);
  // Enhanced debug function with more detailed information
  const logPostsWithTags = useCallback(() => {
    console.log('==== POSTS WITH TAGS ====');
    posts.forEach(post => {
      console.log(`Post ID: ${post.id}, Title: "${post.title}", Published: ${post.published}`);
      console.log('Content preview:', post.content.substring(0, 50) + '...');
      
      if (post.tags && Array.isArray(post.tags)) {
        console.log('Tags:', JSON.stringify(post.tags, null, 2));
      } else {
        console.log('No tags or invalid tag format:', post.tags);
      }
      console.log('-----------------------');
    });
    
    // Test each category separately
    console.log('==== CATEGORY MATCHING ====');
    categories.forEach(category => {
      console.log(`\nTesting category: ${category.name} (ID: ${category.id})`);
      
      // Try to find posts manually for this category
      const matchingPosts = posts.filter(post => {
        if (!post.tags || !Array.isArray(post.tags)) return false;
        
        // Log all tags for this post when checking this category
        console.log(`Checking post "${post.title}" for category "${category.name}"`);
        post.tags.forEach(tagObj => {
          if (tagObj.tag) {
            console.log(`- Tag: slug="${tagObj.tag.slug}", name="${tagObj.tag.name}"`);
          } else {
            console.log(`- Invalid tag object:`, tagObj);
          }
        });
        
        // Check if any tag matches this category
        return post.tags.some(t => {
          if (!t.tag) return false;
          
          const tagSlug = (t.tag.slug || '').toLowerCase().trim();
          const tagName = (t.tag.name || '').toLowerCase().trim();
          const categoryId = category.id.toLowerCase().trim();
          const categoryName = category.name.toLowerCase().trim();
          
          const matches = (
            tagSlug === categoryId ||
            tagName === categoryId ||
            tagSlug === categoryName ||
            tagName === categoryName ||
            // Additional check: partial matching
            tagSlug.includes(categoryId) ||
            categoryId.includes(tagSlug) ||
            tagName.includes(categoryName) ||
            categoryName.includes(tagName)
          );
          
          console.log(`  Match result: ${matches ? 'YES' : 'NO'}`);
          return matches;
        });
      });
        console.log(`Found ${matchingPosts.length} posts for category "${category.name}":`, 
        matchingPosts.map(p => p.title));
    });
  }, [posts, categories]);

  // Improved getPostsByCategory function with more flexible matching
  const getPostsByCategory = (categoryId) => {
    const category = categories.find(c => c.id === categoryId);
    const categoryName = category ? category.name : '';
    
    return posts.filter(post => {
      if (!post.tags || !Array.isArray(post.tags)) return false;
      
      return post.tags.some(t => {
        if (!t.tag) return false;
        
        const tagSlug = (t.tag.slug || '').toLowerCase().trim();
        const tagName = (t.tag.name || '').toLowerCase().trim();
        const catId = categoryId.toLowerCase().trim();
        const catName = categoryName.toLowerCase().trim();
        
        // More flexible matching options
        return (
          tagSlug === catId ||
          tagName === catId ||
          tagSlug === catName ||
          tagName === catName ||
          // Additional matching strategies
          tagSlug.includes(catId) ||
          catId.includes(tagSlug) ||
          tagName.includes(catName) ||
          catName.includes(tagName)
        );
      });
    });
  };
  useEffect(() => {
    if (posts.length > 0 && !loading) {
      logPostsWithTags();
    }
  }, [posts, loading, logPostsWithTags]);

  if (loading) {
    return (
      <>
        <Nav/>
        <div className="w-full max-w-6xl px-4 py-16 mx-auto sm:px-6 lg:px-8">
          <div className="text-center">Memuat data...</div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <Nav/>
        <div className="w-full max-w-6xl px-4 py-16 mx-auto sm:px-6 lg:px-8">
          <div className="text-center text-red-500">{error}</div>
        </div>
      </>
    );
  }

  return (
    <>
      <Nav/>
      <div className="w-full max-w-6xl px-4 py-16 mx-auto sm:px-6 lg:px-8">        <motion.h1 
          className="mb-8 text-3xl font-bold text-center text-primary-800"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Regulasi dan Informasi Publik
        </motion.h1>
        
        {categories.length > 0 ? (
          <TabGroup selectedIndex={selectedIndex} onChange={setSelectedIndex}>
            <TabList className="flex flex-wrap p-1 space-x-1 rounded-xl bg-primary-100">
              {categories.map((category) => (
                <Tab
                  key={category.id}
                  className={({ selected }) =>
                    `flex-grow rounded-lg py-2.5 text-sm font-medium leading-5
                    ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-400 focus:outline-none focus:ring-2
                    ${
                      selected
                        ? 'bg-white text-primary-700 shadow'
                        : 'text-primary-700 hover:bg-primary-700/[0.12] hover:text-white'
                    }`
                  }
                >
                  {category.name}
                </Tab>
              ))}
            </TabList>
            <TabPanels className="mt-4">
              {categories.map((category, idx) => {
                const categoryPosts = getPostsByCategory(category.id);
                
                return (
                  <TabPanel
                    key={idx}
                    className={`rounded-xl bg-white p-4 shadow-md
                      ring-white ring-opacity-60 ring-offset-2 ring-offset-primary-400 focus:outline-none focus:ring-2`}
                  >                    <motion.div
                      {...(prefersReducedMotion ? {} : slideInUp)}
                      transition={{ duration: prefersReducedMotion ? 0 : 0.3 }}
                    >
                      {categoryPosts.length > 0 ? (
                        <div className="space-y-8">
                          {categoryPosts.map(post => (
                            <div key={post.id} className="p-6 bg-white rounded-lg shadow-md">
                              <div 
                                className="prose prose-lg max-w-none"
                                dangerouslySetInnerHTML={{ __html: post.content }}
                              />
                            </div>
                          ))}
                        </div>
                      ) : (
                        <div className="py-8 text-center">
                          <p className="text-gray-600">Belum ada postingan dengan kategori "{category.name}".</p>
                        </div>
                      )}
                    </motion.div>
                  </TabPanel>
                );
              })}
            </TabPanels>
          </TabGroup>
        ) : (
          <div className="p-6 bg-white rounded-lg shadow-md">
            <p className="text-center text-gray-600">Belum ada kategori regulasi yang tersedia. Silakan cek kembali nanti.</p>
          </div>
        )}
      </div>
    </>
  );
}
