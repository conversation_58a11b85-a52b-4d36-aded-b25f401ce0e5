// Test script untuk Enhanced Tags Dashboard
// Jalankan di browser console pada halaman /dashboard/tags

console.log('🧪 Starting Enhanced Tags Dashboard Tests...');

// Test 1: Check if enhanced components are loaded
function testComponentsLoaded() {
    console.log('Test 1: Checking enhanced components...');
    
    const searchBox = document.querySelector('input[placeholder*="Cari"]');
    const sortButtons = document.querySelectorAll('th[role="columnheader"]');
    const pagination = document.querySelector('[role="navigation"][aria-label*="Pagination"]');
    const addButton = document.querySelector('button[aria-label*="Tambah tag"]');
    
    console.log('✅ Search box:', searchBox ? 'Found' : '❌ Missing');
    console.log('✅ Sort buttons:', sortButtons.length > 0 ? `Found ${sortButtons.length}` : '❌ Missing');
    console.log('✅ Pagination:', pagination ? 'Found' : '❌ Missing');
    console.log('✅ Add button:', addButton ? 'Found' : '❌ Missing');
    
    return { searchBox, sortButtons, pagination, addButton };
}

// Test 2: Test keyboard shortcuts
function testKeyboardShortcuts() {
    console.log('Test 2: Testing keyboard shortcuts...');
    
    // Test Ctrl+N shortcut
    const ctrlNEvent = new KeyboardEvent('keydown', {
        key: 'n',
        ctrlKey: true,
        bubbles: true
    });
    
    document.dispatchEvent(ctrlNEvent);
    
    setTimeout(() => {
        const modal = document.querySelector('[role="dialog"]');
        console.log('✅ Ctrl+N shortcut:', modal ? 'Working' : '❌ Not working');
        
        if (modal) {
            // Test ESC key
            const escEvent = new KeyboardEvent('keydown', {
                key: 'Escape',
                bubbles: true
            });
            document.dispatchEvent(escEvent);
            
            setTimeout(() => {
                const modalAfterEsc = document.querySelector('[role="dialog"]');
                console.log('✅ ESC shortcut:', !modalAfterEsc ? 'Working' : '❌ Not working');
            }, 100);
        }
    }, 100);
}

// Test 3: Test search functionality
function testSearchFunctionality() {
    console.log('Test 3: Testing search functionality...');
    
    const searchBox = document.querySelector('input[placeholder*="Cari"]');
    if (searchBox) {
        // Simulate typing in search box
        searchBox.value = 'test';
        searchBox.dispatchEvent(new Event('input', { bubbles: true }));
        
        setTimeout(() => {
            const tableRows = document.querySelectorAll('tbody tr');
            console.log('✅ Search filtering:', tableRows.length >= 0 ? 'Working' : '❌ Not working');
        }, 300);
    } else {
        console.log('❌ Search box not found');
    }
}

// Test 4: Test sorting functionality
function testSortFunctionality() {
    console.log('Test 4: Testing sort functionality...');
    
    const sortButtons = document.querySelectorAll('th[role="columnheader"] button');
    if (sortButtons.length > 0) {
        // Click first sort button
        sortButtons[0].click();
        
        setTimeout(() => {
            const sortIcon = sortButtons[0].querySelector('svg');
            console.log('✅ Sort functionality:', sortIcon ? 'Working' : '❌ Not working');
        }, 100);
    } else {
        console.log('❌ Sort buttons not found');
    }
}

// Test 5: Test accessibility features
function testAccessibilityFeatures() {
    console.log('Test 5: Testing accessibility features...');
    
    const ariaLabels = document.querySelectorAll('[aria-label]');
    const ariaDescriptions = document.querySelectorAll('[aria-describedby]');
    const focusableElements = document.querySelectorAll('button, input, select, textarea, [tabindex]');
    const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    console.log('✅ ARIA labels:', ariaLabels.length);
    console.log('✅ ARIA descriptions:', ariaDescriptions.length);
    console.log('✅ Focusable elements:', focusableElements.length);
    console.log('✅ Heading structure:', headings.length);
    
    // Test focus management
    const firstButton = document.querySelector('button');
    if (firstButton) {
        firstButton.focus();
        console.log('✅ Focus management:', document.activeElement === firstButton ? 'Working' : '❌ Not working');
    }
}

// Test 6: Test API endpoints
async function testAPIEndpoints() {
    console.log('Test 6: Testing API endpoints...');
    
    try {
        // Test GET /api/tags
        const response = await fetch('/api/tags', {
            credentials: 'include'
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ GET /api/tags:', 'Working', `(${data.tags?.length || 0} tags)`);
        } else {
            console.log('❌ GET /api/tags:', `Failed (${response.status})`);
        }
        
        // Test validation by sending invalid data
        const testResponse = await fetch('/api/tags', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            credentials: 'include',
            body: JSON.stringify({ name: '' }) // Invalid empty name
        });
        
        if (testResponse.status === 400) {
            console.log('✅ Input validation:', 'Working (rejected empty name)');
        } else {
            console.log('❌ Input validation:', 'Not working properly');
        }
        
    } catch (error) {
        console.log('❌ API tests failed:', error.message);
    }
}

// Test 7: Test form validation
function testFormValidation() {
    console.log('Test 7: Testing form validation...');
    
    // Open modal first
    const addButton = document.querySelector('button[aria-label*="Tambah tag"]');
    if (addButton) {
        addButton.click();
        
        setTimeout(() => {
            const modal = document.querySelector('[role="dialog"]');
            if (modal) {
                const nameInput = modal.querySelector('input[name="name"]');
                const submitButton = modal.querySelector('button[type="submit"]');
                
                if (nameInput && submitButton) {
                    // Test empty submission
                    nameInput.value = '';
                    submitButton.click();
                    
                    setTimeout(() => {
                        const errorMessage = modal.querySelector('.text-red-500, .text-red-600');
                        console.log('✅ Form validation:', errorMessage ? 'Working' : '❌ Not working');
                        
                        // Close modal
                        const closeButton = modal.querySelector('button[aria-label*="Close"], button[aria-label*="Tutup"]');
                        if (closeButton) closeButton.click();
                    }, 100);
                } else {
                    console.log('❌ Form elements not found in modal');
                }
            } else {
                console.log('❌ Modal did not open');
            }
        }, 100);
    } else {
        console.log('❌ Add button not found');
    }
}

// Test 8: Test performance
function testPerformance() {
    console.log('Test 8: Testing performance...');
    
    const startTime = performance.now();
    
    // Measure page load time
    window.addEventListener('load', () => {
        const loadTime = performance.now() - startTime;
        console.log('✅ Page load time:', `${loadTime.toFixed(2)}ms`);
    });
    
    // Test search performance
    const searchBox = document.querySelector('input[placeholder*="Cari"]');
    if (searchBox) {
        const searchStartTime = performance.now();
        searchBox.value = 'performance test';
        searchBox.dispatchEvent(new Event('input', { bubbles: true }));
        
        setTimeout(() => {
            const searchTime = performance.now() - searchStartTime;
            console.log('✅ Search response time:', `${searchTime.toFixed(2)}ms`);
        }, 100);
    }
}

// Run all tests
function runAllTests() {
    console.log('🚀 Running Enhanced Tags Dashboard Test Suite...\n');
    
    testComponentsLoaded();
    setTimeout(() => testKeyboardShortcuts(), 500);
    setTimeout(() => testSearchFunctionality(), 1000);
    setTimeout(() => testSortFunctionality(), 1500);
    setTimeout(() => testAccessibilityFeatures(), 2000);
    setTimeout(() => testAPIEndpoints(), 2500);
    setTimeout(() => testFormValidation(), 3000);
    setTimeout(() => testPerformance(), 3500);
    
    setTimeout(() => {
        console.log('\n🎉 Test suite completed!');
        console.log('Check console messages above for detailed results.');
        console.log('For manual testing, try:');
        console.log('1. Use Ctrl+N to create new tag');
        console.log('2. Use search box to filter tags');
        console.log('3. Click column headers to sort');
        console.log('4. Navigate with Tab/Shift+Tab');
        console.log('5. Test with screen reader if available');
    }, 4000);
}

// Expose functions globally for manual testing
window.tagTests = {
    runAllTests,
    testComponentsLoaded,
    testKeyboardShortcuts,
    testSearchFunctionality,
    testSortFunctionality,
    testAccessibilityFeatures,
    testAPIEndpoints,
    testFormValidation,
    testPerformance
};

// Auto-run tests if this script is executed
if (typeof window !== 'undefined' && window.location.pathname.includes('/dashboard/tags')) {
    console.log('🏷️ Enhanced Tags Dashboard detected, running automatic tests...');
    setTimeout(runAllTests, 1000); // Wait for page to fully load
} else {
    console.log('ℹ️ To run tests, navigate to /dashboard/tags and call window.tagTests.runAllTests()');
}
