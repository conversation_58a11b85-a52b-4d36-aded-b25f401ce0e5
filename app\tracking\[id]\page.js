'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import Nav from '../../components/Nav';
import { 
  DocumentTextIcon, 
  CalendarIcon,
  UserIcon,
  IdentificationIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';

export default function TrackingPage() {
  const params = useParams();
  const [permohonan, setPermohonan] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (params.id) {
      fetchPermohonan(params.id);
    }
  }, [params.id]);

  const fetchPermohonan = async (id) => {
    try {
      setLoading(true);
      const response = await fetch(`/api/permohonan/${id}`);
      
      if (response.ok) {
        const data = await response.json();
        setPermohonan(data);
      } else if (response.status === 404) {
        setError('Permohonan tidak ditemukan');
      } else {
        setError('Terjadi kesalahan saat mengambil data');
      }
    } catch (err) {
      setError('Terjadi kesalahan koneksi');
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-6 w-6 text-yellow-500" />;
      case 'diproses':
        return <ExclamationTriangleIcon className="h-6 w-6 text-blue-500" />;
      case 'selesai':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'ditolak':
        return <XCircleIcon className="h-6 w-6 text-red-500" />;
      default:
        return <ClockIcon className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'diproses':
        return 'bg-blue-100 text-blue-800';
      case 'selesai':
        return 'bg-green-100 text-green-800';
      case 'ditolak':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Nav />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white rounded-lg shadow p-8">
            <div className="animate-pulse">
              <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
              <div className="space-y-4">
                <div className="h-4 bg-gray-200 rounded w-full"></div>
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Nav />
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="bg-white rounded-lg shadow p-8 text-center">
            <XCircleIcon className="h-16 w-16 text-red-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 mb-2">Permohonan Tidak Ditemukan</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <a
              href="/permohonan"
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            >
              Buat Permohonan Baru
            </a>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Nav />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="bg-white rounded-lg shadow mb-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Tracking Permohonan</h1>
                <p className="text-gray-600">Status dan detail permohonan informasi publik</p>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(permohonan?.status)}
                <span className={`px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(permohonan?.status)}`}>
                  {permohonan?.status?.charAt(0).toUpperCase() + permohonan?.status?.slice(1)}
                </span>
              </div>
            </div>
          </div>
          
          {/* ID Permohonan */}
          <div className="px-6 py-4 bg-blue-50">
            <div className="flex items-center gap-2 mb-2">
              <IdentificationIcon className="h-5 w-5 text-blue-600" />
              <span className="font-semibold text-blue-900">ID Permohonan</span>
            </div>
            <div className="text-xl font-mono font-bold text-blue-700">
              {permohonan?.id}
            </div>
          </div>
        </div>

        {/* Detail Permohonan */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Data Pemohon */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <UserIcon className="h-5 w-5 text-gray-600" />
                Data Pemohon
              </h2>
            </div>
            <div className="px-6 py-4 space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Nama Lengkap</label>
                <p className="text-gray-900 font-medium">{permohonan?.namaSesuaiKtp}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">NIK</label>
                <p className="text-gray-900 font-mono">{permohonan?.nik}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Email</label>
                <p className="text-gray-900">{permohonan?.alamatEmail}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">No. Kontak</label>
                <p className="text-gray-900">{permohonan?.nomorKontak}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Kategori Pemohon</label>
                <p className="text-gray-900">{permohonan?.kategoriPemohon}</p>
              </div>
            </div>
          </div>

          {/* Detail Permohonan */}
          <div className="bg-white rounded-lg shadow">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
                <DocumentTextIcon className="h-5 w-5 text-gray-600" />
                Detail Permohonan
              </h2>
            </div>
            <div className="px-6 py-4 space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-600">Tanggal Permohonan</label>
                <p className="text-gray-900 flex items-center gap-1">
                  <CalendarIcon className="h-4 w-4 text-gray-500" />
                  {formatDate(permohonan?.tanggalPermohonan)}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Bentuk Informasi</label>
                <p className="text-gray-900">{permohonan?.bentukInformasi}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Cara Mendapatkan</label>
                <p className="text-gray-900">{permohonan?.caraMendapatkanInformasi}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Tanggal Dibuat</label>
                <p className="text-gray-900">{formatDate(permohonan?.createdAt)}</p>
              </div>
              {permohonan?.updatedAt && (
                <div>
                  <label className="text-sm font-medium text-gray-600">Terakhir Diupdate</label>
                  <p className="text-gray-900">{formatDate(permohonan?.updatedAt)}</p>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Informasi yang Diminta */}
        <div className="bg-white rounded-lg shadow mt-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Informasi yang Diminta</h2>
          </div>
          <div className="px-6 py-4">
            <p className="text-gray-900 whitespace-pre-wrap">{permohonan?.informasiYangDiminta}</p>
          </div>
        </div>

        {/* Tujuan Permohonan */}
        <div className="bg-white rounded-lg shadow mt-6">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Tujuan Permohonan</h2>
          </div>
          <div className="px-6 py-4">
            <p className="text-gray-900 whitespace-pre-wrap">{permohonan?.tujuanPermohonanInformasi}</p>
          </div>
        </div>

        {/* Catatan Admin */}
        {permohonan?.catatanAdmin && (
          <div className="bg-white rounded-lg shadow mt-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Catatan Admin</h2>
            </div>
            <div className="px-6 py-4">
              <p className="text-gray-900 whitespace-pre-wrap">{permohonan.catatanAdmin}</p>
            </div>
          </div>
        )}

        {/* Tanggapan Admin */}
        {permohonan?.tanggapanAdmin && (
          <div className="bg-white rounded-lg shadow mt-6">
            <div className="px-6 py-4 border-b border-gray-200">
              <h2 className="text-lg font-semibold text-gray-900">Tanggapan Admin</h2>
            </div>
            <div className="px-6 py-4">
              <p className="text-gray-900 whitespace-pre-wrap">{permohonan.tanggapanAdmin}</p>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="bg-white rounded-lg shadow mt-6 p-6 text-center">
          <p className="text-sm text-gray-500 mb-2">
            Untuk pertanyaan lebih lanjut, silakan hubungi PPID BPMP Kalimantan Timur
          </p>
          <p className="text-sm text-gray-500">
            Email: <EMAIL> | Telp: (0541) 123456
          </p>
        </div>
      </div>
    </div>
  );
}
