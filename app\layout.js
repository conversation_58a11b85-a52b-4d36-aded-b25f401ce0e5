import { Inter } from 'next/font/google';
import './globals.css';
import ClientWrapper from './ClientWrapper';
import { AccessibilityProvider } from './context/AccessibilityContext';
import AccessibilityControls from './components/AccessibilityControls';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: {
    default: 'PPID BPMP Prov. Kaltim',
    template: '%s | PPID BPMP Prov. Kaltim'
  },
  description: 'Portal Pejabat Pengelola Informasi dan Dokumentasi BPMP Provinsi Kalimantan Timur. Layanan informasi publik, permohonan informasi, dan keberatan informasi.',
  keywords: 'PPID, BPMP, Kalimantan Timur, informasi publik, permohonan informasi, keberatan, transparansi, akuntabilitas',
  authors: [{ name: 'BPMP Provinsi Kalimantan Timur' }],
  creator: 'BPMP Provinsi Kalimantan Timur',
  publisher: 'BPMP Provinsi Kalimantan Timur',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://ppid-bpmp-kaltim.go.id'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: 'PPID BPMP Prov. Kaltim',
    description: 'Portal Pejabat Pengelola Informasi dan Dokumentasi BPMP Provinsi Kalimantan Timur.',
    url: 'https://ppid-bpmp-kaltim.go.id',
    siteName: 'PPID BPMP Prov. Kaltim',
    locale: 'id_ID',
    type: 'website',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  twitter: {
    card: 'summary_large_image',
    title: 'PPID BPMP Prov. Kaltim',
    description: 'Portal Pejabat Pengelola Informasi dan Dokumentasi BPMP Provinsi Kalimantan Timur.',
  },
  verification: {
    google: 'google-site-verification-code',
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="id">
      <body className={inter.className}>
        <AccessibilityProvider>
          <ClientWrapper>
            {children}
          </ClientWrapper>
          <AccessibilityControls />
        </AccessibilityProvider>
      </body>
    </html>
  );
}
