import { Inter } from 'next/font/google';
import './globals.css';
import ClientWrapper from './ClientWrapper';
import { AccessibilityProvider } from './context/AccessibilityContext';
import AccessibilityControls from './components/AccessibilityControls';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'PPID BPMP Prov. Kaltim',
  description: 'Portal Pejabat Pengelola Informasi dan Dokumentasi',
};

export default function RootLayout({ children }) {
  return (
    <html lang="id">
      <body className={inter.className}>
        <AccessibilityProvider>
          <ClientWrapper>
            {children}
          </ClientWrapper>
          <AccessibilityControls />
        </AccessibilityProvider>
      </body>
    </html>
  );
}
