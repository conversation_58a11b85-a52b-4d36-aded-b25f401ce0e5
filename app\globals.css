@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,400;0,700;1,400;1,700&display=swap');
@import './styles/accessibility.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Global styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  
  body {
    @apply min-h-screen text-gray-800 bg-gradient-to-br from-blue-50 to-purple-50;
  }
  
  h1 {
    @apply text-3xl font-bold md:text-4xl lg:text-5xl;
  }
  
  h2 {
    @apply text-2xl font-bold md:text-3xl lg:text-4xl;
  }
  
  h3 {
    @apply text-xl font-bold md:text-2xl;
  }
}

/* Aksesibilitas - Mode Kontras Tinggi */
.high-contrast {
  --tw-text-opacity: 1;
  --tw-bg-opacity: 1;
  color: rgba(255, 255, 255, var(--tw-text-opacity)) !important;
  background: rgba(0, 0, 0, var(--tw-bg-opacity)) !important;
}

.high-contrast h1, 
.high-contrast h2, 
.high-contrast h3, 
.high-contrast h4, 
.high-contrast h5, 
.high-contrast h6,
.high-contrast p,
.high-contrast a,
.high-contrast button:not(.bg-primary-600):not(.bg-primary-700) {
  color: rgba(255, 255, 255, var(--tw-text-opacity)) !important;
}

.high-contrast a {
  text-decoration: underline;
}

.high-contrast button.bg-primary-600,
.high-contrast button.bg-primary-700 {
  background-color: #ffff00 !important;
  color: #000000 !important;
}

.high-contrast img {
  filter: grayscale(100%) contrast(120%);
}

/* Tambahkan fokus yang jelas untuk aksesibilitas */
@layer base {
  :focus-visible {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-white;
  }
  
  /* Peningkatan kontras untuk teks */
  .text-muted {
    @apply text-gray-700;
  }
  
  /* Ukuran klik yang lebih besar untuk perangkat mobile */
  .clickable {
    @apply min-h-[44px] min-w-[44px];
  }
}

/* Custom component classes */
@layer components {
  .btn {
    @apply px-4 py-2 font-medium transition-colors rounded-md;
  }
  
  .btn-primary {
    @apply text-white bg-primary-600 hover:bg-primary-700;
  }
  
  .btn-secondary {
    @apply text-white bg-secondary-600 hover:bg-secondary-700;
  }
  
  .container-responsive {
    @apply w-full px-4 mx-auto sm:px-6 lg:px-8 max-w-7xl;
  }
  
  .card {
    @apply p-6 transition-shadow bg-white rounded-lg shadow-md hover:shadow-lg;
  }
}

/* Existing styles */
.list-circle {
  list-style-type: circle;
}

html,
body {
  height: 100%;
}

#__next {
  height: 100%;
}

/* FullCalendar responsive fixes */
.fc .fc-toolbar-title {
  font-size: 1.2em;
}

@media (max-width: 768px) {
  .fc .fc-toolbar {
    flex-direction: column;
  }
  .fc .fc-toolbar-title {
    font-size: 1em;
    margin-bottom: 0.5em;
  }
}

/* Custom background gradients */
.bg-gradient-blue-purple {
  @apply bg-gradient-to-br from-primary-50 via-indigo-50 to-purple-100;
}

.bg-gradient-blue-teal {
  @apply bg-gradient-to-br from-primary-50 via-blue-50 to-secondary-50;
}

.bg-gradient-soft-blue {
  @apply bg-gradient-to-r from-blue-50 to-primary-100;
}

.bg-pattern-dots {
  background-color: #f0f9ff;
  background-image: radial-gradient(#0ea5e9 0.5px, transparent 0.5px);
  background-size: 15px 15px;
}

.bg-pattern-grid {
  background-color: #f0f9ff;
  background-image: linear-gradient(rgba(14, 165, 233, 0.1) 1px, transparent 1px),
                    linear-gradient(to right, rgba(14, 165, 233, 0.1) 1px, #f0f9ff 1px);
  background-size: 20px 20px;
}

/* CKEditor custom styles - improved spacing fixes */
.ck-editor__editable_inline {
  min-height: 500px !important;
  padding: 0 !important;
  margin: 0 !important;
}

.ckeditor-container {
  display: block !important;
  line-height: 0 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Reset all margins and paddings for CKEditor elements */
.ck.ck-editor,
.ck.ck-editor__main,
.ck.ck-editor__editable,
.ck.ck-editor__top,
.ck.ck-toolbar,
.ck.ck-content,
.ck-editor__editable_inline {
  margin: 0 !important;
  padding: 0 !important;
  line-height: normal !important;
}

/* Only add padding inside the editable content area */
.ck.ck-editor__editable {
  padding: 1rem !important;
}

/* Remove margin after labels */
label[for="content"] {
  margin-bottom: 0 !important;
  display: block !important;
}

/* Fix for form spacing */
.space-y-0 > * {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Specific fix for the editor container */
div.mb-0.space-y-0 {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

/* Remove any potential whitespace */
.ckeditor-container::before,
.ckeditor-container::after {
  display: none !important;
  content: none !important;
}

/* Adjust spacing between editor and submit button */
form .space-y-0 + .flex {
  margin-top: 1rem !important;
}

/* Maintain toolbar styling */
.ck.ck-toolbar {
  padding: 0.25rem !important;
  border-bottom: 1px solid #c4c4c4 !important;
}

/* Fix toolbar button spacing */
.ck.ck-toolbar > .ck-toolbar__items > * {
  margin-top: 0 !important;
  margin-bottom: 0 !important;
}

/* Fix button spacing in editor */
.ck.ck-button {
  margin: 0.2rem !important;
}

/* Ensure content area has proper padding */
.ck.ck-content {
  padding: 1rem !important;
  min-height: 590px !important; /* Account for borders */
}

/* Fix border styling */
.ck.ck-editor__main {
  border-top: 0 !important;
}

/* Ensure proper border radius */
.ck.ck-editor {
  border-radius: 0.375rem !important;
  overflow: hidden !important;
}

/* Fix toolbar border radius */
.ck.ck-toolbar {
  border-radius: 0.375rem 0.375rem 0 0 !important;
}

/* Fix content area border radius */
.ck.ck-content {
  border-radius: 0 0 0.375rem 0.375rem !important;
}

/* Additional spacing fixes for CKEditor */
.editor-container {
  margin: 0 !important;
  padding: 0 !important;
}

.editor-container label {
  margin-bottom: 0 !important;
  padding-bottom: 0 !important;
}

.editor-container .ckeditor-container {
  margin-top: -1px !important;
}

/* Remove all whitespace between elements */
.editor-container > label + div {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

/* Override any default form spacing */
.space-y-6 > .editor-container {
  margin-top: 1.5rem !important;
}

/* Adjust CKEditor toolbar positioning */
.ck.ck-toolbar {
  padding: 0.25rem !important;
}

/* Increase CKEditor content area height */
.ck.ck-content {
  min-height: 590px !important;
}

/* Force zero spacing between label and editor */
label[for="content"] + * {
  margin-top: 0 !important;
}

/* Fix spacing in Next.js dynamic component */
.editor-container > div {
  margin-top: 0 !important;
}

/* Add these rules to f:\2025\eppid_27-9\app\globals.css */

/* Absolute zero spacing solution */
/*
.zero-spacing-container {
  margin: 0 !important;
  padding: 0 !important;
  line-height: 0 !important;
}

.zero-spacing-container * {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.editor-container {
  margin: 0 !important;
  padding: 0 !important;
}
*/

/* Clean CKEditor styling */
.ck.ck-editor__main {
  min-height: 600px !important;
}

.ck.ck-content {
  min-height: 550px !important; /* Account for toolbar */
  padding: 1rem !important;
}

/* Simple spacing fix for editor */
.ck.ck-editor {
  margin-top: 0 !important;
}

/* Ensure proper border styling */
.ck.ck-editor {
  border: 0 !important;
  border-radius: 0.375rem !important;
  overflow: hidden !important;
}

.ck.ck-toolbar {
  border: 1px solid #d1d5db !important;
  border-bottom: none !important;
  border-radius: 0.375rem 0.375rem 0 0 !important;
}

.ck.ck-content {
  border: 1px solid #d1d5db !important;
  border-top: none !important;
  border-radius: 0 0 0.375rem 0.375rem !important;
}

/* Add to f:\2025\eppid_27-9\app\globals.css */

/* Clean CKEditor styling */
.ckeditor-root {
  min-height: auto !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Make editor take up exactly the right amount of space */
.ckeditor-root .ck.ck-editor {
  border: none !important;
  box-shadow: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Fix toolbar styling */
.ckeditor-root .ck.ck-toolbar {
  border: none !important;
  border-bottom: 1px solid #e5e7eb !important;
  box-shadow: none !important;
  background: transparent !important;
  padding: 0.5rem 0 !important;
  margin: 0 !important;
}

/* Fix toolbar button spacing */
.ckeditor-root .ck.ck-toolbar > .ck-toolbar__items > * {
  margin: 0 2px !important;
}

/* Set proper content area styling */
.ckeditor-root .ck.ck-content {
  min-height: 590px !important;
  border: none !important;
  box-shadow: none !important;
  padding: 1rem 0 !important;
  margin: 0 !important;
  background: white !important;
}

/* Fix focus states */
.ckeditor-root .ck.ck-editor__editable.ck-focused {
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* Remove any dynamic borders */
.ckeditor-root .ck.ck-editor__main {
  border: none !important;
}

/* Ensure content has proper padding */
.ckeditor-root .dynamic-editor-content {
  min-height: 590px !important;
}

/* Add to f:\2025\eppid_27-9\app\globals.css */

/* Document-style editor container */
.main-container {
    --ckeditor5-preview-height: 700px;
    font-family: 'Lato', sans-serif;
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    position: relative;
}

/* Typography for editor content */
.ck-content {
    font-family: 'Lato', sans-serif;
    line-height: 1.6;
    word-break: break-word;
}

/* Editor wrapper layout */
.editor-container__editor-wrapper {
    display: flex;
    width: 100%;
    max-height: var(--ckeditor5-preview-height);
    min-height: var(--ckeditor5-preview-height);
    overflow-y: auto;
    background: var(--ck-color-base-foreground);
}

/* Document editor border */
.editor-container_document-editor {
    border: 1px solid var(--ck-color-base-border);
    width: 100%;
}

/* Toolbar styling */
.editor-container_document-editor .editor-container__toolbar {
    display: flex;
    position: relative;
    box-shadow: 0 2px 3px hsla(0, 0%, 0%, 0.078);
}

/* Toolbar width */
.editor-container_document-editor .editor-container__toolbar > .ck.ck-toolbar {
    flex-grow: 1;
    width: 100%;
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top: 0;
    border-left: 0;
    border-right: 0;
}

/* Menu bar styling */
.editor-container_document-editor .editor-container__menu-bar > .ck.ck-menu-bar {
    border-bottom-right-radius: 0;
    border-bottom-left-radius: 0;
    border-top: 0;
    border-left: 0;
    border-right: 0;
}

/* Editor area with scrolling */
.editor-container_document-editor .editor-container__editor {
    margin-top: 28px;
    margin-bottom: 28px;
    display: flex;
    justify-content: center;
    width: 100%;
}

/* A4 paper-like styling */
.editor-container_document-editor .editor-container__editor .ck.ck-editor__editable {
    box-sizing: border-box;
    width: 210mm;
    min-height: 297mm;
    height: fit-content;
    padding: 20mm 12mm;
    border: 1px hsl(0, 0%, 82.7%) solid;
    background: hsl(0, 0%, 100%);
    box-shadow: 0 2px 3px hsla(0, 0%, 0%, 0.078);
    margin: 0 auto;
}

/* Responsive adjustments */
@media (max-width: 1300px) {
  .editor-container_document-editor .editor-container__editor .ck.ck-editor__editable {
    width: calc(100% - 40px);
    min-width: auto;
    max-width: 210mm;
    margin: 0 20px;
  }
}

/* No border when editor is focused */
.ck.ck-editor__editable_inline.ck-focused {
  border: 1px hsl(0, 0%, 82.7%) solid !important;
  box-shadow: 0 0 5px hsla(0, 0%, 0%, 0.1) !important;
}

/* Ensure content area has proper font */
.ck.ck-content {
  font-family: 'Lato', sans-serif !important;
}

/* Remove extra borders */
.ck.ck-editor__main {
  border: none !important;
}

/* Print optimization */
@media print {
  .editor-container__toolbar {
    display: none !important;
  }
  
  .editor-container__editor-wrapper {
    overflow: visible !important;
    height: auto !important;
    max-height: none !important;
  }
  
  .editor-container__editor .ck.ck-editor__editable {
    box-shadow: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }
}

/* Add to your globals.css file */

/* Document-style editor container */
.main-container {
  position: relative;
  width: 100%;
  font-family: 'Lato', sans-serif;
}

/* Make editor content area look like a document */
.ck-editor__editable {
  min-height: 700px !important;
  padding: 2rem !important;
  font-family: 'Lato', sans-serif !important;
  line-height: 1.6 !important;
}

/* Style the content */
.ck-content {
  font-family: 'Lato', sans-serif;
  line-height: 1.6;
  word-break: break-word;
}

/* Style toolbar */
.ck-toolbar {
  border-bottom: 1px solid #e5e7eb !important;
  background-color: #f9fafb !important;
}

/* Improve button styling */
.ck.ck-button {
  padding: 0.4em !important;
}

/* Focus styling */
.ck.ck-editor__editable.ck-focused:not(.ck-editor__nested-editable) {
  border: 1px solid #d1d5db !important;
  box-shadow: none !important;
}

/* Print optimization */
@media print {
  .ck-toolbar {
    display: none !important;
  }
  
  .ck-editor__editable {
    box-shadow: none !important;
    margin: 0 !important;
  }
}