import { NextResponse } from 'next/server';
import { registerUser } from '../../lib/auth';

export async function POST(request) {
  try {
    const { username, email, password } = await request.json();
    
    const result = await registerUser(username, email, password);
    
    if (result.success) {
      return NextResponse.json({
        success: true,
        user: result.user,
      });
    } else {
      return NextResponse.json({
        success: false,
        error: result.error,
      }, { status: 400 });
    }
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: '<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han pada server',
    }, { status: 500 });
  }
}