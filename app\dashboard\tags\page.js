'use client';

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';
import { PlusIcon } from '@heroicons/react/24/outline';
import { useAuth } from '../../context/AuthContext';
import EnhancedTagsTable from '../../components/EnhancedTagsTable';
import TagFormModal from '../../components/TagFormModal';
import ConfirmDeleteModal from '../../components/ConfirmDeleteModal';

export default function TagsPage() {
  const [tags, setTags] = useState([]);
  const [loading, setLoading] = useState(true);
  const [showModal, setShowModal] = useState(false);
  const [editingTag, setEditingTag] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortConfig, setSortConfig] = useState({ key: 'name', direction: 'asc' });
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(10);
  // State untuk modal konfirmasi hapus
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [deletingTag, setDeletingTag] = useState(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const router = useRouter();
  const { user } = useAuth();

  // Check admin permissions
  useEffect(() => {
    console.log("Current user:", user);
    console.log("User role:", user?.role);
    
    if (user) {
      const isAdmin = user.role && user.role.toLowerCase() === 'admin';
      console.log("Is admin check:", isAdmin);
      
      if (!isAdmin) {
        toast.error('Anda tidak memiliki izin untuk mengelola tag');
        router.push('/dashboard');
      }
    }
  }, [user, router]);

  // Handle unauthorized responses
  const handleUnauthorizedResponse = useCallback((response) => {
    if (response.status === 401) {
      toast.error('Sesi Anda telah berakhir. Silakan login kembali.');
      router.push('/login');
      return true;
    } else if (response.status === 403) {
      toast.error('Anda tidak memiliki izin untuk melakukan aksi ini');
      return true;
    }
    return false;
  }, [router]);

  // Fetch tags with enhanced error handling
  const fetchTags = useCallback(async () => {
    try {
      setLoading(true);
      console.log('Fetching tags from API...');
      
      const timestamp = new Date().getTime();
      const response = await fetch(`/api/tags?t=${timestamp}`, {
        credentials: 'include',
        cache: 'no-store'
      });
      
      console.log(`Fetch response status: ${response.status}`);
      
      if (!response.ok) {
        if (handleUnauthorizedResponse(response)) {
          return;
        }
        throw new Error('Failed to fetch tags');
      }
      
      const data = await response.json();
      console.log('Fetched tags:', data.tags);
      setTags(data.tags || []);
    } catch (error) {
      console.error('Error fetching tags:', error);
      toast.error('Terjadi kesalahan saat mengambil data tag');
    } finally {
      setLoading(false);
    }
  }, [handleUnauthorizedResponse]);

  useEffect(() => {
    fetchTags();
  }, [fetchTags]);

  // Handle tag operations
  const handleOpenModal = (tag = null) => {
    setEditingTag(tag);
    setShowModal(true);
  };

  const handleCloseModal = () => {
    setShowModal(false);
    setEditingTag(null);
  };

  const handleSaveTag = async (formData) => {
    try {
      const url = editingTag 
        ? `/api/tags/${editingTag.id}` 
        : '/api/tags';
      
      const method = editingTag ? 'PUT' : 'POST';
      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json'
        },
        credentials: 'include',
        body: JSON.stringify(formData)
      });
      
      if (handleUnauthorizedResponse(response)) {
        return false;
      }
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Terjadi kesalahan');
      }
      
      // Refresh tags list
      await fetchTags();
      
      // Show success message
      toast.success(editingTag 
        ? 'Tag berhasil diperbarui' 
        : 'Tag baru berhasil dibuat'
      );
      
      return true;
    } catch (error) {
      console.error('Error saving tag:', error);
      toast.error(error.message || 'Terjadi kesalahan saat menyimpan tag');
      return false;
    }
  };
  const handleDeleteTag = async (id, name) => {
    // Buka modal konfirmasi
    setDeletingTag({ id, name });
    setShowDeleteModal(true);
  };

  const confirmDeleteTag = async () => {
    if (!deletingTag) return;
    
    try {
      setIsDeleting(true);
      console.log(`Attempting to delete tag with ID: ${deletingTag.id}`);
      
      const response = await fetch(`/api/tags/${deletingTag.id}`, {
        method: 'DELETE',
        credentials: 'include'
      });
      
      console.log(`Delete response status: ${response.status}`);
      
      if (handleUnauthorizedResponse(response)) {
        return;
      }
      
      const data = await response.json();
      console.log('Delete response data:', data);
      
      if (!response.ok) {
        throw new Error(data.error || 'Terjadi kesalahan saat menghapus');
      }
      
      console.log('Refreshing tags list after deletion');
      await fetchTags();
      
      toast.success(`Tag "${deletingTag.name}" berhasil dihapus`);
      
      // Tutup modal dan reset state
      setShowDeleteModal(false);
      setDeletingTag(null);
      
    } catch (error) {
      console.error('Error deleting tag:', error);
      toast.error(error.message || 'Terjadi kesalahan saat menghapus tag');
    } finally {
      setIsDeleting(false);
    }
  };
  const cancelDeleteTag = () => {
    setShowDeleteModal(false);
    setDeletingTag(null);
    setIsDeleting(false);
  };
  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event) => {
      // Escape to close modal
      if (event.key === 'Escape' && showModal) {
        handleCloseModal();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [showModal]);

  // Filter and sort tags
  const filteredAndSortedTags = tags
    .filter(tag => 
      tag.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (tag.description && tag.description.toLowerCase().includes(searchTerm.toLowerCase()))
    )
    .sort((a, b) => {
      const aValue = a[sortConfig.key] || '';
      const bValue = b[sortConfig.key] || '';
      
      if (sortConfig.direction === 'asc') {
        return aValue.localeCompare(bValue);
      } else {
        return bValue.localeCompare(aValue);
      }
    });

  // Pagination
  const totalPages = Math.ceil(filteredAndSortedTags.length / pageSize);
  const paginatedTags = filteredAndSortedTags.slice(
    (currentPage - 1) * pageSize,
    currentPage * pageSize
  );

  return (
    <div className="p-6" role="main" aria-label="Halaman Kelola Tag">
      {/* Header with accessibility */}      <div className="flex items-center justify-between mb-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Kelola Tag</h1>
          <p className="mt-1 text-sm text-gray-600">
            Kelola tag untuk mengorganisir konten dan informasi
          </p>
        </div>
        <button
          onClick={() => handleOpenModal()}
          className="flex items-center px-4 py-2 text-sm font-medium text-white transition-colors rounded-md bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
          aria-label="Tambah tag baru"
        >
          <PlusIcon className="w-5 h-5 mr-2" aria-hidden="true" />
          Tambah Tag
        </button>
      </div>

      {/* Enhanced Tags Table */}
      
      {loading ? (
        <div className="flex items-center justify-center h-40" role="status" aria-label="Memuat data tag">
          <div className="w-8 h-8 border-4 border-gray-200 rounded-full border-t-primary-600 animate-spin"></div>
          <span className="sr-only">Memuat...</span>
        </div>
      ) : (
        <EnhancedTagsTable
          tags={paginatedTags}
          totalTags={filteredAndSortedTags.length}
          currentPage={currentPage}
          pageSize={pageSize}
          totalPages={totalPages}
          searchTerm={searchTerm}
          sortConfig={sortConfig}
          onSearchChange={setSearchTerm}
          onSortChange={setSortConfig}
          onPageChange={setCurrentPage}
          onPageSizeChange={(newSize) => {
            setPageSize(newSize);
            setCurrentPage(1);
          }}
          onEdit={handleOpenModal}
          onDelete={handleDeleteTag}
          isLoading={loading}
        />
      )}
        {/* Enhanced Modal */}
      <TagFormModal
        isOpen={showModal}
        onClose={handleCloseModal}
        onSave={handleSaveTag}
        editingTag={editingTag}
      />
      
      {/* Modal Konfirmasi Hapus */}
      <ConfirmDeleteModal
        isOpen={showDeleteModal}
        onClose={cancelDeleteTag}
        onConfirm={confirmDeleteTag}
        tagName={deletingTag?.name || ''}
        isLoading={isDeleting}
      />
    </div>
  );
}