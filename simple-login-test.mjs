// Simple Login Test
import { createRefreshToken } from './app/lib/auth.js';

async function testLoginFix() {
  try {
    console.log('Testing refresh token creation...');
    const testUserId = 'test-user-123';
    const token = await createRefreshToken(testUserId);
    console.log('SUCCESS: Refresh token created!', token ? 'Token exists' : 'No token');
    return true;
  } catch (error) {
    console.log('ERROR:', error.message);
    return false;
  }
}

testLoginFix().then(success => {
  console.log('Test result:', success ? 'PASSED' : 'FAILED');
  process.exit(success ? 0 : 1);
});
