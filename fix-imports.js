const fs = require('fs');
const path = require('path');

// Function to calculate relative path from one file to another
function getRelativePath(fromFile, toDir) {
    const fromDir = path.dirname(fromFile);
    let relativePath = path.relative(fromDir, toDir);
    
    // Convert Windows backslashes to forward slashes for imports
    relativePath = relativePath.replace(/\\/g, '/');
    
    // Add ./ prefix if needed
    if (!relativePath.startsWith('../') && !relativePath.startsWith('./')) {
        relativePath = './' + relativePath;
    }
    
    return relativePath;
}

// Function to fix imports in a file
function fixImportsInFile(filePath) {
    try {
        let content = fs.readFileSync(filePath, 'utf8');
        let changed = false;
          // Replace @/lib/... imports
        const libRegex = /from ['"]@\/lib\/([^'"]+)['"]/g;
        content = content.replace(libRegex, (match, libFile) => {
            const relativePath = getRelativePath(filePath, path.join(__dirname, 'lib'));
            changed = true;
            return `from '${relativePath}/${libFile}'`;
        });
        
        // Replace @/app/lib/... imports  
        const appLibRegex = /from ['"]@\/app\/lib\/([^'"]+)['"]/g;
        content = content.replace(appLibRegex, (match, libFile) => {
            const relativePath = getRelativePath(filePath, path.join(__dirname, 'app', 'lib'));
            changed = true;
            return `from '${relativePath}/${libFile}'`;
        });

        // Replace @/app/... imports (components, etc.)
        const appRegex = /from ['"]@\/app\/([^'"]+)['"]/g;
        content = content.replace(appRegex, (match, appFile) => {
            const relativePath = getRelativePath(filePath, path.join(__dirname, 'app'));
            changed = true;
            return `from '${relativePath}/${appFile}'`;
        });

        // Replace import('@/app/...') dynamic imports
        const dynamicImportRegex = /import\(['"]@\/app\/([^'"]+)['"]\)/g;
        content = content.replace(dynamicImportRegex, (match, appFile) => {
            const relativePath = getRelativePath(filePath, path.join(__dirname, 'app'));
            changed = true;
            return `import('${relativePath}/${appFile}')`;
        });
        
        if (changed) {
            fs.writeFileSync(filePath, content);
            console.log(`Fixed imports in: ${filePath}`);
        }
    } catch (error) {
        console.error(`Error processing ${filePath}:`, error.message);
    }
}

// Function to recursively find and fix JS files
function fixImportsRecursively(dir) {
    const items = fs.readdirSync(dir);
    
    for (const item of items) {
        const itemPath = path.join(dir, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory() && !item.startsWith('.') && item !== 'node_modules') {
            fixImportsRecursively(itemPath);
        } else if (stat.isFile() && (item.endsWith('.js') || item.endsWith('.jsx'))) {
            fixImportsInFile(itemPath);
        }
    }
}

console.log('Starting import path fixes...');
console.log('Current directory:', __dirname);
console.log('Target app directory:', path.join(__dirname, 'app'));
fixImportsRecursively(path.join(__dirname, 'app'));
console.log('Import path fixes completed!');
