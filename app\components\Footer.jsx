// components/Footer.js
"use client";
import React from 'react';
import { FaFacebook, FaInstagram, FaWhatsapp, FaYoutube } from 'react-icons/fa';

const Footer = () => {
  const socialLinks = [
    { icon: FaFacebook, href: "https://fb.com/ultbpmpkaltim", label: "Facebook BPMP Kaltim" },
    { icon: FaInstagram, href: "https://www.instagram.com/bpmpprovkaltim", label: "Instagram BPMP Kaltim" },
    { icon: FaWhatsapp, href: "https://wa.me/+6282148788787", label: "WhatsApp BPMP Kaltim" },
    { icon: FaYoutube, href: "https://www.youtube.com/@BPMPProvinsiKalimantanTimur", label: "YouTube BPMP Kaltim" },
  ];

  return (
    <footer className="py-6 mt-auto shadow-lg bg-gradient-to-r from-primary-700 to-primary-900" role="contentinfo" aria-label="Footer PPID BPMP Provinsi Kaltim">
      <div className="container px-4 mx-auto">
        <div className="flex flex-col items-center justify-between gap-4 md:flex-row">
          {/* Logo dan Teks */}
          <div className="text-center md:text-left">
            <h3 className="mb-2 text-xl font-bold text-white">PPID BPMP Provinsi Kaltim</h3>
            <p className="text-sm text-white">Pejabat Pengelola Informasi dan Dokumentasi</p>
            <address className="mt-2 text-sm not-italic text-white">
              <p>Jl. Cipto Mangunkusumo, Samarinda, Kaltim</p>
              <p>Email: <a href="mailto:<EMAIL>" className="underline hover:text-primary-200 focus:outline-none focus:ring-2 focus:ring-primary-200"><EMAIL></a></p>
            </address>
          </div>
          
          {/* Tautan Penting */}
          <nav className="hidden md:block" aria-label="Tautan Footer">
            <h4 className="mb-2 font-semibold text-white">Tautan Penting</h4>
            <ul className="space-y-1 text-sm">
              <li><a href="/profil" className="text-white transition-colors hover:text-primary-200 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-200">Profil</a></li>
              <li><a href="/informasi" className="text-white transition-colors hover:text-primary-200 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-200">Informasi Publik</a></li>
              <li><a href="/regulasi" className="text-white transition-colors hover:text-primary-200 hover:underline focus:outline-none focus:ring-2 focus:ring-primary-200">Regulasi</a></li>
            </ul>
          </nav>
          
          {/* Social Media Links */}
          <div className="flex justify-center mb-2 space-x-4" aria-label="Media Sosial">
            {socialLinks.map((link, index) => (
              <a
                key={index}
                href={link.href}
                target="_blank"
                rel="noopener noreferrer"
                aria-label={link.label}
                className="p-2 text-white transition-all rounded-full bg-primary-600 hover:bg-primary-500 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-700"
                data-tts-enabled
              >
                <link.icon className="text-xl" aria-hidden="true" />
              </a>
            ))}
          </div>
        </div>
        
        <div className="pt-4 mt-4 text-center border-t border-primary-600">
          <p className="text-sm text-white">
            © {new Date().getFullYear()} PPID BPMP Provinsi Kaltim. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
