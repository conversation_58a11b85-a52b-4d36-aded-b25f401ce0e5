import Link from 'next/link';
import Image from 'next/image';
import { prisma } from '../../../lib/prisma';
import Nav from '../../components/Nav';
import { formatDate } from '../../../lib/utils';
import { notFound } from 'next/navigation';
import Sidebar from '../../components/Sidebar';

export async function generateMetadata(props) {
  const params = await props.params;
  const { slug } = params;
  const post = await prisma.post.findUnique({
    where: { slug },
  });

  if (!post) {
    return {
      title: 'Post tidak ditemukan',
    };
  }

  return {
    title: post.title,
    description: post.excerpt || `${post.title} - PPID BPMP Prov. Kaltim`,
  };
}

async function getPost(slug) {
  const post = await prisma.post.findUnique({
    where: {
      slug,
      published: true,
    },
    include: {
      tags: {
        include: {
          tag: true,
        },
      },
      author: {
        select: {
          username: true,
        },
      },
    },
  });
  
  return post;
}

export default async function PostPage(props) {
  const params = await props.params;
  const { slug } = params;
  const post = await getPost(slug);

  if (!post) {
    notFound();
  }

  return (
    <div className="flex flex-col min-h-screen md:flex-row">
      <Nav />
      <div className="hidden md:block">
        <Sidebar theme="light" />
      </div>
      <article className="flex-1 w-full max-w-4xl px-4 py-16 mx-auto sm:px-6 lg:px-8">
        <div className="mb-8">
          <div className="flex flex-wrap gap-2 mb-4">
            {post.tags.map(({ tag }) => (
              <Link 
                key={tag.id} 
                href={`/posts/tag/${tag.slug}`}
                className="px-3 py-1 text-sm font-medium rounded-full text-primary-700 bg-primary-100 hover:bg-primary-200"
              >
                {tag.name}
              </Link>
            ))}
          </div>
          
          <h1 className="mb-4 text-3xl font-bold text-gray-900 md:text-4xl">
            {post.title}
          </h1>
          
          <div className="flex items-center gap-4 mb-6 text-sm text-gray-600">
            <span>Oleh: {post.author.username}</span>
            <span>•</span>
            <time dateTime={post.publishedAt}>{formatDate(post.publishedAt)}</time>
          </div>
          
          {post.featuredImage && (
            <div className="relative w-full h-64 mb-8 overflow-hidden rounded-lg md:h-96">
              <Image
                src={post.featuredImage}
                alt={post.title}
                fill
                className="object-cover"
                priority
              />
            </div>
          )}
        </div>
        
        <div 
          className="prose prose-lg max-w-none prose-primary"
          dangerouslySetInnerHTML={{ __html: post.content }}
        />
      </article>
    </div>
  );
}
