// Enhanced Prisma schema untuk Tag model dengan optimasi performa
// Update untuk schema.prisma - bagian Tag models

// Model untuk Tag dengan optimasi
model EnhancedTag {
  id          String        @id @default(uuid()) @db.VarChar(36)
  name        String        @unique @db.VarChar(100)  // Explicit length limit
  slug        String        @unique @db.VarChar(120)  // Slightly longer for URL safety
  description String?       @db.Text
  
  // Metadata fields
  isActive    Boolean       @default(true)            // Soft delete capability
  priority    Int           @default(0)               // For ordering tags
  color       String?       @db.VarChar(7)           // Hex color for UI
  icon        String?       @db.VarChar(50)          // Icon identifier
  
  // Audit fields
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt
  createdBy   String?       @db.VarChar(36)          // Track who created
  updatedBy   String?       @db.VarChar(36)          // Track who updated
  
  // Relations
  posts       EnhancedTagsOnPosts[]
  creator     User?         @relation("EnhancedTagCreator", fields: [createdBy], references: [id], onDelete: SetNull)
  updater     User?         @relation("EnhancedTagUpdater", fields: [updatedBy], references: [id], onDelete: SetNull)

  // Enhanced indexing for better performance
  @@index([slug])                          // Existing index
  @@index([name])                          // For search by name
  @@index([isActive])                      // For filtering active tags
  @@index([priority, name])                // For ordered listing
  @@index([createdAt])                     // For chronological queries
  @@index([createdBy])                     // For creator queries
  @@fulltext([name, description])          // Full-text search capability
  
  @@map("tags")                            // Explicit table name
}

// Enhanced many-to-many relation with metadata
model EnhancedTagsOnPosts {
  postId    String   @db.VarChar(36)
  tagId     String   @db.VarChar(36)
  
  // Metadata for the relationship
  addedAt   DateTime @default(now())        // When tag was added to post
  addedBy   String?  @db.VarChar(36)       // Who added the tag
  
  // Relations
  post      Post     @relation(fields: [postId], references: [id], onDelete: Cascade)
  tag       EnhancedTag      @relation(fields: [tagId], references: [id], onDelete: Cascade)
  adder     User?    @relation("PostTagAdder", fields: [addedBy], references: [id], onDelete: SetNull)

  @@id([postId, tagId])
  @@index([postId])                        // Existing indexes
  @@index([tagId])
  @@index([addedAt])                       // For chronological queries
  @@index([addedBy])                       // For tracking who added tags
  
  @@map("tags_on_posts")                   // Explicit table name
}


// Add these relations to your existing User model:
// tagsCreated    EnhancedTag[]         @relation("EnhancedTagCreator")
// tagsUpdated    EnhancedTag[]         @relation("EnhancedTagUpdater") 
// postTagsAdded  TagsOnPosts[] @relation("PostTagAdder")

// Optional: Add a TagCategory model for hierarchical organization
model TagCategory {
  id          String   @id @default(uuid()) @db.VarChar(36)
  name        String   @unique @db.VarChar(100)
  slug        String   @unique @db.VarChar(120)
  description String?  @db.Text
  color       String?  @db.VarChar(7)
  icon        String?  @db.VarChar(50)
  parentId    String?  @db.VarChar(36)
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  
  // Self-referential relation for hierarchy
  parent      TagCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children    TagCategory[] @relation("CategoryHierarchy")
  
  // Audit fields
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  createdBy   String?  @db.VarChar(36)
  
  creator     User?    @relation("CategoryCreator", fields: [createdBy], references: [id], onDelete: SetNull)
  
  @@index([slug])
  @@index([parentId])
  @@index([isActive, sortOrder])
  @@fulltext([name, description])
  
  @@map("tag_categories")
}

// If using TagCategory, add relation to Tag model:
// In Tag model, add:
// categoryId  String?      @db.VarChar(36)
// category    TagCategory? @relation(fields: [categoryId], references: [id], onDelete: SetNull)
// @@index([categoryId])   // Add to Tag indexes
