// Login Fix Verification Report
const fs = require('fs');
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function verifyLoginFix() {
  const report = [];
  report.push('🔍 LOGIN ERROR FIX VERIFICATION REPORT');
  report.push('=====================================');
  report.push(`Date: ${new Date().toISOString()}`);
  report.push('');

  try {
    // Test 1: Check database connection
    report.push('📋 Test 1: Database Connection');
    await prisma.$connect();
    report.push('✅ Database connection successful');
    
    // Test 2: Check if refreshtoken model exists
    report.push('');
    report.push('📋 Test 2: RefreshToken Model Access');
    try {
      const count = await prisma.refreshtoken.count();
      report.push(`✅ refreshtoken model accessible (lowercase) - Count: ${count}`);
    } catch (error) {
      report.push(`❌ refreshtoken model error: ${error.message}`);
      
      try {
        const count2 = await prisma.refreshToken.count();
        report.push(`✅ refreshToken model accessible (camelCase) - Count: ${count2}`);
      } catch (error2) {
        report.push(`❌ refreshToken model error: ${error2.message}`);
      }
    }
    
    // Test 3: Test createRefreshToken function
    report.push('');
    report.push('📋 Test 3: CreateRefreshToken Function');
    try {
      const { createRefreshToken } = await import('./app/lib/auth.js');
      
      // Create a test refresh token
      const testUserId = 'test-user-id-verification';
      const token = await createRefreshToken(testUserId);
      report.push('✅ createRefreshToken function works');
      report.push(`Token created successfully (length: ${token.length})`);
      
      // Clean up test token
      await prisma.refreshtoken.deleteMany({
        where: { userId: testUserId }
      });
      report.push('✅ Test token cleaned up');
      
    } catch (error) {
      report.push(`❌ createRefreshToken error: ${error.message}`);
      report.push(`Stack: ${error.stack}`);
    }
    
    // Test 4: Check schema integrity
    report.push('');
    report.push('📋 Test 4: Schema Models Available');
    const models = Object.keys(prisma).filter(key => !key.startsWith('$') && !key.startsWith('_'));
    report.push(`Available models: ${models.join(', ')}`);
    
    report.push('');
    report.push('🎯 CONCLUSION:');
    if (report.some(line => line.includes('createRefreshToken function works'))) {
      report.push('✅ LOGIN ERROR FIXED SUCCESSFULLY!');
      report.push('🚀 The refresh token creation is now working properly.');
    } else {
      report.push('❌ LOGIN ERROR STILL PERSISTS');
      report.push('⚠️  Additional troubleshooting required.');
    }
    
  } catch (error) {
    report.push(`❌ Verification failed: ${error.message}`);
  } finally {
    await prisma.$disconnect();
  }
  
  // Write report to file
  const reportContent = report.join('\n');
  fs.writeFileSync('LOGIN-FIX-VERIFICATION-REPORT.md', reportContent);
  
  console.log('✅ Verification complete! Check LOGIN-FIX-VERIFICATION-REPORT.md for results.');
}

verifyLoginFix().catch(console.error);
