@echo off
echo ==========================================
echo    PRISMA MIGRATION - PERMOHONAN INFORMASI
echo ==========================================
echo.

echo 1. Checking current migration status...
npx prisma migrate status
echo.

echo 2. Generating new migration for Permohonan Informasi...
npx prisma migrate dev --name add_permohonan_informasi
echo.

echo 3. Generating Prisma Client...
npx prisma generate
echo.

echo 4. Verifying database structure...
npx prisma db pull
echo.

echo ==========================================
echo    MIGRATION COMPLETED SUCCESSFULLY!
echo ==========================================
echo.
echo Table 'permohonan_informasi' has been created with the following structure:
echo - id (VARCHAR 36, Primary Key)
echo - tanggalPermohonan (DATETIME)
echo - kategoriPemohon (VARCHAR)
echo - nik (VARCHAR 20)
echo - namaSesuaiKtp (VARCHAR 255)
echo - alamatLengkapSesuaiKtp (TEXT)
echo - alamatTinggalSaatIni (TEXT)
echo - nomorKontak (VARCHAR 20)
echo - alamatEmail (VARCHAR 255)
echo - pekerjaan (VARCHAR 255)
echo - informasiYangDiminta (TEXT)
echo - tujuanPermohonanInformasi (TEXT)
echo - bentukInformasi (VARCHAR)
echo - caraMendapatkanInformasi (VARCHAR)
echo - formulirPermohonanPath (VARCHAR 500, Optional)
echo - suratPernyataanPath (VARCHAR 500, Optional)
echo - scanKtpPath (VARCHAR 500, Optional)
echo - status (VARCHAR, Default: 'pending')
echo - catatanAdmin (TEXT, Optional)
echo - tanggapanAdmin (TEXT, Optional)
echo - adminId (VARCHAR 36, Optional)
echo - createdAt (DATETIME)
echo - updatedAt (DATETIME)
echo.
echo Indexes created for:
echo - status
echo - kategoriPemohon
echo - createdAt
echo - adminId
echo.
echo Next steps:
echo 1. Start development server: npm run dev
echo 2. Access form at: http://localhost:3000/permohonan
echo 3. Access admin panel at: http://localhost:3000/admin/permohonan
echo.
pause
