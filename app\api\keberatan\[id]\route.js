import { NextRequest, NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request, { params }) {
  try {
    const { id } = params;

    // Validate ID format (6 digits)
    if (!id || !/^\d{6}$/.test(id)) {
      return NextResponse.json(
        { error: 'ID keberatan harus 6 digit angka' },
        { status: 400 }
      );
    }

    // Find keberatan by ID
    const keberatan = await prisma.keberatanInformasi.findUnique({
      where: { id }
    });

    if (!keberatan) {
      return NextResponse.json(
        { error: 'Keberatan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Return keberatan data (excluding sensitive admin fields for public access)
    const publicData = {
      id: keberatan.id,
      tanggalPermohonan: keberatan.tanggalPermohonan,
      kategoriPemohon: keberatan.kategoriPemohon,
      namaSesuaiKtp: keberatan.namaSesuaiKtp,
      alamatEmail: keberatan.alamatEmail,
      nomorKontak: keberatan.nomorKontak,
      topikKeberatan: keberatan.topikKeberatan,
      maksudKeberatan: keberatan.maksudKeberatan,
      alasanKeberatan: keberatan.alasanKeberatan,
      status: keberatan.status,
      catatanAdmin: keberatan.catatanAdmin,
      tanggapanAdmin: keberatan.tanggapanAdmin,
      createdAt: keberatan.createdAt,
      updatedAt: keberatan.updatedAt
    };

    return NextResponse.json({
      success: true,
      data: publicData
    });

  } catch (error) {
    console.error('Error fetching keberatan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan server' },
      { status: 500 }
    );
  }
}

export async function PATCH(request, { params }) {
  try {
    const { id } = params;
    const body = await request.json();

    // Validate ID format (6 digits)
    if (!id || !/^\d{6}$/.test(id)) {
      return NextResponse.json(
        { error: 'ID keberatan harus 6 digit angka' },
        { status: 400 }
      );
    }

    // Check if keberatan exists
    const existingKeberatan = await prisma.keberatanInformasi.findUnique({
      where: { id }
    });

    if (!existingKeberatan) {
      return NextResponse.json(
        { error: 'Keberatan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Validate allowed fields for update
    const allowedFields = ['status', 'catatanAdmin', 'tanggapanAdmin', 'adminId'];
    const updateData = {};

    for (const field of allowedFields) {
      if (body[field] !== undefined) {
        updateData[field] = body[field];
      }
    }

    // Validate status if provided
    if (updateData.status) {
      const validStatuses = ['pending', 'diproses', 'selesai', 'ditolak'];
      if (!validStatuses.includes(updateData.status)) {
        return NextResponse.json(
          { error: 'Status tidak valid' },
          { status: 400 }
        );
      }
    }

    // Update keberatan
    const updatedKeberatan = await prisma.keberatanInformasi.update({
      where: { id },
      data: updateData
    });

    return NextResponse.json({
      success: true,
      message: 'Keberatan berhasil diupdate',
      data: updatedKeberatan
    });

  } catch (error) {
    console.error('Error updating keberatan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan server' },
      { status: 500 }
    );
  }
}

export async function DELETE(request, { params }) {
  try {
    const { id } = params;

    // Validate ID format (6 digits)
    if (!id || !/^\d{6}$/.test(id)) {
      return NextResponse.json(
        { error: 'ID keberatan harus 6 digit angka' },
        { status: 400 }
      );
    }

    // Check if keberatan exists
    const existingKeberatan = await prisma.keberatanInformasi.findUnique({
      where: { id }
    });

    if (!existingKeberatan) {
      return NextResponse.json(
        { error: 'Keberatan tidak ditemukan' },
        { status: 404 }
      );
    }

    // Delete keberatan
    await prisma.keberatanInformasi.delete({
      where: { id }
    });

    return NextResponse.json({
      success: true,
      message: 'Keberatan berhasil dihapus'
    });

  } catch (error) {
    console.error('Error deleting keberatan:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan server' },
      { status: 500 }
    );
  }
}
