// Test script for permohonan informasi API
console.log('Testing Permohonan Informasi API...');

async function testPermohonanAPI() {
  try {
    // Test GET endpoint
    console.log('1. Testing GET /api/permohonan...');
    const getResponse = await fetch('http://localhost:3000/api/permohonan');
    console.log('GET Response Status:', getResponse.status);
    
    if (getResponse.ok) {
      const data = await getResponse.json();
      console.log('GET Response Data:', data);
    } else {
      console.log('GET Response Error:', await getResponse.text());
    }

    // Test POST endpoint with sample data
    console.log('\n2. Testing POST /api/permohonan...');
    const formData = new FormData();
    formData.append('tanggalPermohonan', '2025-06-09');
    formData.append('kategori<PERSON>emohon', 'Perorangan');
    formData.append('nik', '1234567890123456');
    formData.append('namaSesuaiKtp', 'Test User');
    formData.append('alamatLengkapSesuaiKtp', 'Jl. Test No. 123');
    formData.append('alamatTinggalSaatIni', 'Jl. Test No. 123');
    formData.append('nomorKontak', '081234567890');
    formData.append('alamatEmail', '<EMAIL>');
    formData.append('pekerjaan', 'Programmer');
    formData.append('informasiYangDiminta', 'Data PPID BPMP');
    formData.append('tujuanPermohonanInformasi', 'Penelitian');
    formData.append('bentukInformasi', 'Soft Copy');
    formData.append('caraMendapatkanInformasi', 'Email');

    const postResponse = await fetch('http://localhost:3000/api/permohonan', {
      method: 'POST',
      body: formData
    });

    console.log('POST Response Status:', postResponse.status);
    
    if (postResponse.ok) {
      const data = await postResponse.json();
      console.log('POST Response Data:', data);
    } else {
      console.log('POST Response Error:', await postResponse.text());
    }

  } catch (error) {
    console.error('Test Error:', error);
  }
}

// Run the test if this script is executed directly
if (typeof window === 'undefined') {
  testPermohonanAPI();
}
