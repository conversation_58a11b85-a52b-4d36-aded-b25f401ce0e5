'use client';

import { useEffect } from 'react';

/**
 * This component initializes and checks the Speech Synthesis API
 * It helps ensure that text-to-speech functionality works across browsers
 */
export default function SpeechSynthesisInitializer() {
  useEffect(() => {
    // Only run on client-side
    if (typeof window === 'undefined') return;
    
    // Check if browser supports speech synthesis
    if (!window.speechSynthesis) {
      console.warn('Speech Synthesis API not supported in this browser');
      
      // Create a fallback or mock speech synthesis for unsupported browsers
      if (!window.mockSpeechSynthesisCreated) {
        window.speechSynthesis = {
          speaking: false,
          paused: false,
          pending: false,
          getVoices: () => [],
          cancel: () => {},
          pause: () => {},
          resume: () => {},
          speak: () => {
            console.log('Speech synthesis not supported - speak called with mock implementation');
          }
        };
        
        // Add a constructor for SpeechSynthesisUtterance if it doesn't exist
        if (!window.SpeechSynthesisUtterance) {
          window.SpeechSynthesisUtterance = function(text) {
            this.text = text;
            this.onend = null;
            this.onerror = null;
            this.onstart = null;
          };
        }
        
        window.mockSpeechSynthesisCreated = true;
      }
      return;
    }

    // Ensure getVoices works properly
    const ensureVoicesLoaded = () => {
      return new Promise((resolve) => {
        let voices = window.speechSynthesis.getVoices();
        
        if (voices.length > 0) {
          resolve(voices);
          return;
        }
        
        // If no voices are initially available, wait for the voiceschanged event
        const voicesChangedHandler = () => {
          voices = window.speechSynthesis.getVoices();
          resolve(voices);
          
          window.speechSynthesis.removeEventListener('voiceschanged', voicesChangedHandler);
        };
        
        window.speechSynthesis.addEventListener('voiceschanged', voicesChangedHandler);
        
        // Safety timeout in case the event never fires
        setTimeout(() => {
          window.speechSynthesis.removeEventListener('voiceschanged', voicesChangedHandler);
          resolve(window.speechSynthesis.getVoices());
        }, 1000);
      });
    };

    // Initialize voice loading
    ensureVoicesLoaded().then(voices => {
      console.log(`Speech synthesis initialized with ${voices.length} voices available`);
      
      // Pre-initialize the speech engine with a silent utterance
      try {
        const silentUtterance = new SpeechSynthesisUtterance('');
        silentUtterance.volume = 0;
        silentUtterance.rate = 0.1;
        
        // Add safety handler for any errors during initialization
        silentUtterance.onerror = (err) => {
          console.log('Silent utterance error during initialization:', err);
          // Don't report this error to the user
        };
        
        window.speechSynthesis.speak(silentUtterance);
        window.speechSynthesis.cancel(); // Immediately cancel it
      } catch (e) {
        console.warn('Error during speech synthesis initialization:', e);
      }
    });
    
    // Fix for Chrome and some browsers that suspend speechSynthesis after a period of inactivity
    const keepSpeechAlive = () => {
      if (window.speechSynthesis) {
        // Only interact with speechSynthesis if it's actually speaking
        if (window.speechSynthesis.speaking) {
          try {
            window.speechSynthesis.pause();
            window.speechSynthesis.resume();
          } catch (e) {
            console.warn('Error during speech keep-alive:', e);
          }
        }
      }
    };
    
    // Run keep-alive less frequently (every 5 seconds instead of every 10s)
    // to reduce the chance of interruptions
    const resumeTimer = setInterval(keepSpeechAlive, 5000);

    // Additional browser-specific fixes
    const applyBrowserFixes = () => {
      // Detect browser
      const isChrome = navigator.userAgent.indexOf('Chrome') !== -1;
      const isSafari = navigator.userAgent.indexOf('Safari') !== -1 && navigator.userAgent.indexOf('Chrome') === -1;
      const isFirefox = navigator.userAgent.indexOf('Firefox') !== -1;
      const isEdge = navigator.userAgent.indexOf('Edg') !== -1;
      
      // Apply fixes for specific browsers
      if (isSafari) {
        // Safari sometimes needs utterances to be explicitly cancelled
        const originalSpeak = window.speechSynthesis.speak;
        window.speechSynthesis.speak = function(utterance) {
          window.speechSynthesis.cancel(); // Cancel any ongoing speech first
          originalSpeak.call(window.speechSynthesis, utterance);
        };
      }
      
      if (isChrome || isEdge) {
        // Chrome and Edge sometimes stop speech synthesis when tab is inactive
        document.addEventListener('visibilitychange', () => {
          if (document.visibilityState === 'visible' && window.speechSynthesis.speaking && window.speechSynthesis.paused) {
            window.speechSynthesis.resume();
          }
        });
      }
    };
    
    applyBrowserFixes();

    return () => {
      clearInterval(resumeTimer);
      // Cancel any ongoing speech when component unmounts
      if (window.speechSynthesis) {
        window.speechSynthesis.cancel();
      }
    };
  }, []);

  // This component doesn't render anything
  return null;
}
