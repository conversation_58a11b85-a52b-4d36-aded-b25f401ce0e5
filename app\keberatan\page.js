'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { 
  FileText, 
  Upload, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Label } from '../components/ui/label';
import { Input } from '../components/ui/input';
import { Textarea } from '../components/ui/textarea';
import { Button } from '../components/ui/button';
import { Alert, AlertDescription } from '../components/ui/alert';

export default function KeberatanInformasiPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    tanggalPermohonan: new Date().toISOString().split('T')[0],
    kategoriPemohon: '',
    nik: '',
    namaSesuaiKtp: '',
    alamatLengkapSesuaiKtp: '',
    alamatTinggalSaatIni: '',
    nomorKontak: '',
    alamatEmail: '',
    pekerjaan: '',
    topikKeberatan: '',
    maksudKeberatan: '',
    alasanKeberatan: '',
    pernyataanKeberatan: false
  });

  const [files, setFiles] = useState({
    salinanKtp: null
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  const handleFileChange = (e, fileType) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file size (max 1MB)
      if (file.size > 1024 * 1024) {
        setErrors(prev => ({ ...prev, [fileType]: 'Ukuran file maksimal 1MB' }));
        return;
      }
      
      // Validate file type
      const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf'];
      if (!allowedTypes.includes(file.type)) {
        setErrors(prev => ({ ...prev, [fileType]: 'Format file harus JPG, PNG, atau PDF' }));
        return;
      }
      
      setFiles(prev => ({ ...prev, [fileType]: file }));
      setErrors(prev => ({ ...prev, [fileType]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    const requiredFields = [
      'kategoriPemohon', 'nik', 'namaSesuaiKtp', 'alamatLengkapSesuaiKtp',
      'alamatTinggalSaatIni', 'nomorKontak', 'alamatEmail', 'pekerjaan',
      'topikKeberatan', 'maksudKeberatan', 'alasanKeberatan'
    ];

    requiredFields.forEach(field => {
      if (!formData[field] || formData[field].trim() === '') {
        newErrors[field] = 'Field ini wajib diisi';
      }
    });

    // Email validation
    if (formData.alamatEmail && !/\S+@\S+\.\S+/.test(formData.alamatEmail)) {
      newErrors.alamatEmail = 'Format email tidak valid';
    }

    // NIK validation (16 digits)
    if (formData.nik && !/^\d{16}$/.test(formData.nik)) {
      newErrors.nik = 'NIK harus 16 digit angka';
    }

    // Phone validation
    if (formData.nomorKontak && !/^[\d\-\+\(\)\s]+$/.test(formData.nomorKontak)) {
      newErrors.nomorKontak = 'Format nomor kontak tidak valid';
    }

    // File validation
    if (!files.salinanKtp) {
      newErrors.salinanKtp = 'Salinan KTP wajib diupload';
    }

    // Pernyataan validation
    if (!formData.pernyataanKeberatan) {
      newErrors.pernyataanKeberatan = 'Anda harus menyetujui pernyataan';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      setSubmitStatus({ type: 'error', message: 'Mohon lengkapi semua field yang wajib diisi' });
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      const submitData = new FormData();
      
      // Append form data
      Object.keys(formData).forEach(key => {
        submitData.append(key, formData[key]);
      });
      
      // Append files
      if (files.salinanKtp) {
        submitData.append('salinanKtp', files.salinanKtp);
      }

      const response = await fetch('/api/keberatan', {
        method: 'POST',
        body: submitData,
      });

      const result = await response.json();

      if (response.ok) {
        setSubmitStatus({ 
          type: 'success', 
          message: `Keberatan berhasil dikirim! ID Keberatan: ${result.id}`,
          id: result.id
        });
        
        // Reset form
        setFormData({
          tanggalPermohonan: new Date().toISOString().split('T')[0],
          kategoriPemohon: '',
          nik: '',
          namaSesuaiKtp: '',
          alamatLengkapSesuaiKtp: '',
          alamatTinggalSaatIni: '',
          nomorKontak: '',
          alamatEmail: '',
          pekerjaan: '',
          topikKeberatan: '',
          maksudKeberatan: '',
          alasanKeberatan: '',
          pernyataanKeberatan: false
        });
        setFiles({ salinanKtp: null });
        
        // Redirect after 3 seconds
        setTimeout(() => {
          router.push('/');
        }, 3000);
      } else {
        setSubmitStatus({ 
          type: 'error', 
          message: result.error || 'Terjadi kesalahan saat mengirim keberatan' 
        });
      }
    } catch (error) {
      console.error('Error submitting form:', error);
      setSubmitStatus({ 
        type: 'error', 
        message: 'Terjadi kesalahan koneksi. Silakan coba lagi.' 
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen py-8 bg-gray-50">
      <div className="max-w-4xl px-4 mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          {/* Header */}
          <div className="mb-8 text-center">
            <h1 className="mb-4 text-3xl font-bold text-gray-900">
              Keberatan Atas Permohonan Informasi Publik PPID BPMP Provinsi Kalimantan Timur
            </h1>
            <p className="max-w-2xl mx-auto text-gray-600">
              Mohon Anda isi data dan atau informasi di bawah ini dengan lengkap dan benar.
            </p>
          </div>

          {/* Status Alert */}
          {submitStatus && (
            <Alert className={`mb-6 ${submitStatus.type === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>
              <div className="flex items-center">
                {submitStatus.type === 'success' ? (
                  <CheckCircle className="w-4 h-4 text-green-600" />
                ) : (
                  <AlertCircle className="w-4 h-4 text-red-600" />
                )}
                <AlertDescription className={`ml-2 ${submitStatus.type === 'success' ? 'text-green-800' : 'text-red-800'}`}>
                  {submitStatus.message}
                  {submitStatus.id && (
                    <div className="p-2 mt-2 bg-white border rounded">
                      <strong>ID Keberatan: {submitStatus.id}</strong>
                      <br />
                      <small>Simpan ID ini untuk melacak status keberatan Anda di halaman informasi.</small>
                    </div>
                  )}
                </AlertDescription>
              </div>
            </Alert>
          )}

          {/* Form */}
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Data Pemohon */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="w-5 h-5" />
                  Data Pemohon
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="tanggalPermohonan">Tanggal permohonan keberatan *</Label>
                    <Input
                      id="tanggalPermohonan"
                      name="tanggalPermohonan"
                      type="date"
                      value={formData.tanggalPermohonan}
                      onChange={handleInputChange}
                      className={errors.tanggalPermohonan ? 'border-red-500' : ''}
                    />
                    {errors.tanggalPermohonan && (
                      <p className="mt-1 text-sm text-red-600">{errors.tanggalPermohonan}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="kategoriPemohon">Kategori pemohon *</Label>
                    <div className="mt-2 space-y-2">
                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="kategoriPemohon"
                          value="Pemohon"
                          checked={formData.kategoriPemohon === 'Pemohon'}
                          onChange={handleInputChange}
                          className="text-blue-600"
                        />
                        <span>Pemohon</span>
                      </label>
                      <label className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="kategoriPemohon"
                          value="Pemberi kuasa"
                          checked={formData.kategoriPemohon === 'Pemberi kuasa'}
                          onChange={handleInputChange}
                          className="text-blue-600"
                        />
                        <span>Pemberi kuasa</span>
                      </label>
                    </div>
                    {errors.kategoriPemohon && (
                      <p className="mt-1 text-sm text-red-600">{errors.kategoriPemohon}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="nik">Nomor Induk Kependudukan (NIK) *</Label>
                    <Input
                      id="nik"
                      name="nik"
                      type="text"
                      placeholder="Jawaban Anda"
                      value={formData.nik}
                      onChange={handleInputChange}
                      className={errors.nik ? 'border-red-500' : ''}
                      maxLength="16"
                    />
                    {errors.nik && (
                      <p className="mt-1 text-sm text-red-600">{errors.nik}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="namaSesuaiKtp">Nama lengkap sesuai KTP *</Label>
                    <Input
                      id="namaSesuaiKtp"
                      name="namaSesuaiKtp"
                      type="text"
                      placeholder="Jawaban Anda"
                      value={formData.namaSesuaiKtp}
                      onChange={handleInputChange}
                      className={errors.namaSesuaiKtp ? 'border-red-500' : ''}
                    />
                    {errors.namaSesuaiKtp && (
                      <p className="mt-1 text-sm text-red-600">{errors.namaSesuaiKtp}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="alamatLengkapSesuaiKtp">Alamat lengkap sesuai KTP *</Label>
                    <Textarea
                      id="alamatLengkapSesuaiKtp"
                      name="alamatLengkapSesuaiKtp"
                      placeholder="Jawaban Anda"
                      value={formData.alamatLengkapSesuaiKtp}
                      onChange={handleInputChange}
                      className={errors.alamatLengkapSesuaiKtp ? 'border-red-500' : ''}
                      rows={3}
                    />
                    {errors.alamatLengkapSesuaiKtp && (
                      <p className="mt-1 text-sm text-red-600">{errors.alamatLengkapSesuaiKtp}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="alamatTinggalSaatIni">Alamat tinggal saat ini *</Label>
                    <Textarea
                      id="alamatTinggalSaatIni"
                      name="alamatTinggalSaatIni"
                      placeholder="Jawaban Anda"
                      value={formData.alamatTinggalSaatIni}
                      onChange={handleInputChange}
                      className={errors.alamatTinggalSaatIni ? 'border-red-500' : ''}
                      rows={3}
                    />
                    {errors.alamatTinggalSaatIni && (
                      <p className="mt-1 text-sm text-red-600">{errors.alamatTinggalSaatIni}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div>
                    <Label htmlFor="nomorKontak">Nomor kontak (HP/ WA) *</Label>
                    <Input
                      id="nomorKontak"
                      name="nomorKontak"
                      type="text"
                      placeholder="Jawaban Anda"
                      value={formData.nomorKontak}
                      onChange={handleInputChange}
                      className={errors.nomorKontak ? 'border-red-500' : ''}
                    />
                    {errors.nomorKontak && (
                      <p className="mt-1 text-sm text-red-600">{errors.nomorKontak}</p>
                    )}
                  </div>

                  <div>
                    <Label htmlFor="alamatEmail">Alamat surat elektronik (email) *</Label>
                    <Input
                      id="alamatEmail"
                      name="alamatEmail"
                      type="email"
                      placeholder="Jawaban Anda"
                      value={formData.alamatEmail}
                      onChange={handleInputChange}
                      className={errors.alamatEmail ? 'border-red-500' : ''}
                    />
                    {errors.alamatEmail && (
                      <p className="mt-1 text-sm text-red-600">{errors.alamatEmail}</p>
                    )}
                  </div>
                </div>

                <div>
                  <Label htmlFor="pekerjaan">Pekerjaan *</Label>
                  <Input
                    id="pekerjaan"
                    name="pekerjaan"
                    type="text"
                    placeholder="Jawaban Anda"
                    value={formData.pekerjaan}
                    onChange={handleInputChange}
                    className={errors.pekerjaan ? 'border-red-500' : ''}
                  />
                  {errors.pekerjaan && (
                    <p className="mt-1 text-sm text-red-600">{errors.pekerjaan}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Detail Keberatan */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  Detail Keberatan
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="topikKeberatan">Topik keberatan atas permohonan informasi yang diminta *</Label>
                  <Textarea
                    id="topikKeberatan"
                    name="topikKeberatan"
                    placeholder="Jawaban Anda"
                    value={formData.topikKeberatan}
                    onChange={handleInputChange}
                    className={errors.topikKeberatan ? 'border-red-500' : ''}
                    rows={3}
                  />
                  {errors.topikKeberatan && (
                    <p className="mt-1 text-sm text-red-600">{errors.topikKeberatan}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="maksudKeberatan">Maksud keberatan atas permohonan informasi *</Label>
                  <Textarea
                    id="maksudKeberatan"
                    name="maksudKeberatan"
                    placeholder="Jawaban Anda"
                    value={formData.maksudKeberatan}
                    onChange={handleInputChange}
                    className={errors.maksudKeberatan ? 'border-red-500' : ''}
                    rows={3}
                  />
                  {errors.maksudKeberatan && (
                    <p className="mt-1 text-sm text-red-600">{errors.maksudKeberatan}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="alasanKeberatan">Alasan mengajukan keberatan *</Label>
                  <div className="mt-2 space-y-2">
                    {[
                      'Pengecualian informasi',
                      'Tidak ditanggapinya permohonan informasi publik',
                      'Permintaan informasi publik tidak sebagaimana yang diminta',
                      'Tidak dipenuhinya permohonan informasi publik',
                      'Pengenaan biaya yang tidak wajar',
                      'Penyampaian informasi publik melebihi jangka waktu',
                      'Yang lain:'
                    ].map((option) => (
                      <label key={option} className="flex items-center space-x-2">
                        <input
                          type="radio"
                          name="alasanKeberatan"
                          value={option}
                          checked={formData.alasanKeberatan === option}
                          onChange={handleInputChange}
                          className="text-blue-600"
                        />
                        <span>{option}</span>
                      </label>
                    ))}
                  </div>
                  {errors.alasanKeberatan && (
                    <p className="mt-1 text-sm text-red-600">{errors.alasanKeberatan}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Upload File */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Upload className="w-5 h-5" />
                  Upload Dokumen
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="salinanKtp">Salinan KTP/ surat kuasa pemohon *</Label>
                  <p className="mb-2 text-sm text-gray-600">Upload 1 file yang didukung: image. Maks 1 MB.</p>
                  <Input
                    id="salinanKtp"
                    name="salinanKtp"
                    type="file"
                    accept="image/*,.pdf"
                    onChange={(e) => handleFileChange(e, 'salinanKtp')}
                    className={errors.salinanKtp ? 'border-red-500' : ''}
                  />
                  {files.salinanKtp && (
                    <p className="mt-1 text-sm text-green-600">
                      File terpilih: {files.salinanKtp.name}
                    </p>
                  )}
                  {errors.salinanKtp && (
                    <p className="mt-1 text-sm text-red-600">{errors.salinanKtp}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Pernyataan */}
            <Card>
              <CardContent className="pt-6">
                <div className="space-y-4">
                  <div className="p-4 rounded-lg bg-gray-50">
                    <p className="text-sm text-gray-700">
                      Demikian keberatan atas permohonan informasi publik ini saya buat dengan
                      sebenar-benarnya. Apabila di kemudian hari terdapat keterangan yang tidak
                      benar, saya bersedia dituntut dimuka pengadilan sesuai dengan ketentuan
                      perundang-undangan yang berlaku serta bersedia menerima segala tindakan
                      yang diambil oleh BPMP Provinsi Kalimantan Timur.
                    </p>
                  </div>

                  <label className="flex items-start space-x-2">
                    <input
                      type="checkbox"
                      name="pernyataanKeberatan"
                      checked={formData.pernyataanKeberatan}
                      onChange={handleInputChange}
                      className="mt-1 text-blue-600"
                    />
                    <span className="text-sm">Ya. Saya setuju.</span>
                  </label>
                  {errors.pernyataanKeberatan && (
                    <p className="text-sm text-red-600">{errors.pernyataanKeberatan}</p>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Submit Button */}
            <div className="flex justify-center">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="px-8 py-3 font-medium text-white bg-blue-600 rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                    Mengirim...
                  </>
                ) : (
                  'Kirim'
                )}
              </Button>
            </div>
          </form>
        </motion.div>
      </div>
    </div>
  );
}
