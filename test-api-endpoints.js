// Test script for Permohonan Informasi API endpoints
// Run with: node test-api-endpoints.js

const BASE_URL = 'http://localhost:3000';

async function testAPI() {
  console.log('🧪 Testing Permohonan Informasi API Endpoints\n');

  try {
    // Test GET /api/permohonan
    console.log('1. Testing GET /api/permohonan...');
    const getResponse = await fetch(`${BASE_URL}/api/permohonan`);
    const getData = await getResponse.json();
    console.log(`   Status: ${getResponse.status}`);
    console.log(`   Response: ${JSON.stringify(getData).substring(0, 100)}...\n`);

    // Test POST /api/permohonan (sample data)
    console.log('2. Testing POST /api/permohonan...');
    const sampleData = {
      namaSesuaiKtp: 'Test User',
      nik: '1234567890123456',
      alamatEmail: '<EMAIL>',
      nomorTelepon: '081234567890',
      alamatLengkap: 'Jl. Test No. 123',
      peker<PERSON><PERSON>: 'Tester',
      jenis<PERSON><PERSON><PERSON>: 'laki-laki',
      kategoriPemohon: 'perorangan',
      informasiDiminta: 'Test information request',
      tujuanPenggunaan: 'Testing purposes',
      caraMendapat: 'melihat_langsung',
      caraMemberi: 'elektronik'
    };

    const postResponse = await fetch(`${BASE_URL}/api/permohonan`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(sampleData),
    });
    const postData = await postResponse.json();
    console.log(`   Status: ${postResponse.status}`);
    console.log(`   Response: ${JSON.stringify(postData).substring(0, 100)}...\n`);

    if (postData.success && postData.data?.id) {
      const testId = postData.data.id;
      
      // Test GET /api/permohonan/[id]
      console.log(`3. Testing GET /api/permohonan/${testId}...`);
      const getByIdResponse = await fetch(`${BASE_URL}/api/permohonan/${testId}`);
      const getByIdData = await getByIdResponse.json();
      console.log(`   Status: ${getByIdResponse.status}`);
      console.log(`   Response: ${JSON.stringify(getByIdData).substring(0, 100)}...\n`);

      // Test PATCH /api/permohonan/[id]
      console.log(`4. Testing PATCH /api/permohonan/${testId}...`);
      const patchResponse = await fetch(`${BASE_URL}/api/permohonan/${testId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: 'diproses' }),
      });
      const patchData = await patchResponse.json();
      console.log(`   Status: ${patchResponse.status}`);
      console.log(`   Response: ${JSON.stringify(patchData).substring(0, 100)}...\n`);

      // Test DELETE /api/permohonan/[id]
      console.log(`5. Testing DELETE /api/permohonan/${testId}...`);
      const deleteResponse = await fetch(`${BASE_URL}/api/permohonan/${testId}`, {
        method: 'DELETE',
      });
      const deleteData = await deleteResponse.json();
      console.log(`   Status: ${deleteResponse.status}`);
      console.log(`   Response: ${JSON.stringify(deleteData).substring(0, 100)}...\n`);
    }

    console.log('✅ API endpoint testing completed!');
  } catch (error) {
    console.error('❌ Error testing API:', error);
  }
}

// Only run if this script is executed directly
if (require.main === module) {
  testAPI();
}

module.exports = { testAPI };
