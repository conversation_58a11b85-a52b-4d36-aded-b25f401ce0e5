import { NextResponse } from 'next/server';
import { prisma } from '../../lib/prisma';
import bcrypt from 'bcryptjs';
import { createAccessToken, createRefreshToken } from '../../lib/auth';

export async function POST(request) {
  try {
    const { username, password } = await request.json();
    
    if (!username || !password) {
      return NextResponse.json(
        { error: 'Username dan password diperlukan' },
        { status: 400 }
      );
    }
    
    // Cari user berdasarkan username atau email
    const user = await prisma.user.findFirst({
      where: {
        OR: [
          { username },
          { email: username }
        ]
      }
    });
    
    if (!user) {
      return NextResponse.json(
        { error: 'Username atau password salah' },
        { status: 401 }
      );
    }
    
    // Verifikasi password menggunakan compare dari bcryptjs
    const isValid = await bcrypt.compare(password, user.passwordHash);
    if (!isValid) {
      return NextResponse.json(
        { error: 'Username atau password salah' },
        { status: 401 }
      );
    }
      // Buat payload untuk token
    const tokenPayload = {
      id: user.id,
      username: user.username,
      role: user.role ? user.role.toLowerCase() : 'user'
    };
    
    // Buat access token dan refresh token
    const accessToken = await createAccessToken(tokenPayload);
    const refreshToken = await createRefreshToken(user.id);
      // Hapus password dari respons
    const { passwordHash, salt, ...userWithoutPassword } = user;
    
    // Normalize role to lowercase
    if (userWithoutPassword.role) {
      userWithoutPassword.role = userWithoutPassword.role.toLowerCase();
    }
    
    console.log('User data after login:', { 
      id: userWithoutPassword.id, 
      username: userWithoutPassword.username, 
      role: userWithoutPassword.role 
    });
    
    // Set cookies
    const response = NextResponse.json({ user: userWithoutPassword }, { status: 200 });
    
    // Set access token cookie (1 jam)
    response.cookies.set({
      name: 'token',
      value: accessToken,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
      maxAge: 60 * 60 // 1 jam
    });
    
    // Set refresh token cookie (7 hari)
    response.cookies.set({
      name: 'refresh_token',
      value: refreshToken,
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      path: '/',
      maxAge: 7 * 24 * 60 * 60 // 7 hari
    });
    
    console.log('Cookies:', request.cookies.getAll());
      return response;
  } catch (error) {
    console.error('Login error:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat login' },
      { status: 500 }
    );
  }
}
