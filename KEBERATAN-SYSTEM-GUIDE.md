# Sistem Keberatan Atas Permohonan Informasi Publik

Sistem keberatan telah berhasil diimplementasi untuk PPID BPMP Provinsi Kalimantan Timur dengan fitur-fitur lengkap sesuai dengan form yang diberikan.

## Fitur Yang Telah Diimplementasi

### 1. Database Schema
- **Model**: `KeberatanInformasi` di `prisma/schema.prisma`
- **ID**: 6 digit angka (seperti permohonan informasi)
- **Fields**: Semua field sesuai dengan form yang diberikan
- **Status tracking**: pending, diproses, selesai, ditolak
- **File upload**: Salinan KTP/surat kuasa

### 2. Halaman Formulir Keberatan (`/keberatan`)
- **Lokasi**: `app/keberatan/page.js`
- **URL**: `http://localhost:3000/keberatan`
- **Fitur**:
  - Form responsif dengan validasi lengkap
  - Upload file salinan KTP (max 1MB)
  - Validasi email, NIK 16 digit, nomor kontak
  - Pernyataan persetujuan wajib
  - Success notification dengan ID keberatan
  - Auto redirect ke homepage setelah submit

### 3. API Endpoints

#### POST `/api/keberatan`
- **Lokasi**: `app/api/keberatan/route.js`
- **Fungsi**: Menerima dan menyimpan data keberatan
- **Fitur**:
  - Validasi file upload (ukuran dan tipe)
  - Penyimpanan file ke `public/uploads/keberatan/`
  - Generate ID 6 digit unik
  - Validasi form lengkap
  - Response dengan ID keberatan

#### GET `/api/keberatan`
- **Fungsi**: Mendapatkan semua data keberatan (untuk admin)
- **Limit**: 50 record terbaru

#### GET `/api/keberatan/[id]`
- **Lokasi**: `app/api/keberatan/[id]/route.js`
- **Fungsi**: Tracking keberatan berdasarkan ID
- **Validasi**: ID harus 6 digit angka

#### PATCH `/api/keberatan/[id]`
- **Fungsi**: Update status keberatan (untuk admin)
- **Fields**: status, catatanAdmin, tanggapanAdmin, adminId

#### DELETE `/api/keberatan/[id]`
- **Fungsi**: Hapus keberatan (untuk admin)

### 4. Dashboard Admin (`/dashboard/keberatan`)
- **Lokasi**: `app/dashboard/keberatan/page.js`
- **URL**: `http://localhost:3000/dashboard/keberatan`
- **Fitur**:
  - Statistik keberatan (total, pending, diproses, selesai, ditolak)
  - Tabel daftar keberatan dengan filter dan search
  - Modal detail keberatan lengkap
  - Update status keberatan
  - View file salinan KTP
  - Responsive design

### 5. Tracking Terintegrasi di `/informasi`
- **Lokasi**: `app/informasi/page.js`
- **Fitur**:
  - Tracking gabungan permohonan dan keberatan
  - Auto-detect jenis berdasarkan ID
  - Display berbeda untuk permohonan vs keberatan
  - Link ke form keberatan

### 6. Card Component Update
- **Lokasi**: `app/components/cardData.jsx`
- **Update**: Card "Formulir Keberatan" sekarang mengarah ke `/keberatan`

## Form Fields Sesuai Gambar

### Data Pemohon
- Tanggal permohonan keberatan (auto-filled)
- Kategori pemohon (radio: Pemohon/Pemberi kuasa)
- NIK (16 digit, validasi)
- Nama lengkap sesuai KTP
- Alamat lengkap sesuai KTP
- Alamat tinggal saat ini
- Nomor kontak (HP/WA)
- Alamat email (validasi format)
- Pekerjaan

### Detail Keberatan
- Topik keberatan atas permohonan informasi yang diminta
- Maksud keberatan atas permohonan informasi
- Alasan mengajukan keberatan (radio options):
  - Pengecualian informasi
  - Tidak ditanggapinya permohonan informasi publik
  - Permintaan informasi publik tidak sebagaimana yang diminta
  - Tidak dipenuhinya permohonan informasi publik
  - Pengenaan biaya yang tidak wajar
  - Penyampaian informasi publik melebihi jangka waktu
  - Yang lain

### Upload Dokumen
- Salinan KTP/surat kuasa pemohon (required)
- Format: Image/PDF, max 1MB

### Pernyataan
- Checkbox persetujuan pernyataan (required)
- Teks pernyataan sesuai dengan form asli

## Cara Menggunakan

### Untuk User (Pemohon)
1. **Akses Form**: Buka `http://localhost:3000/informasi`
2. **Klik Card**: "Formulir Keberatan"
3. **Isi Form**: Lengkapi semua field yang required (*)
4. **Upload File**: Upload salinan KTP
5. **Setujui**: Centang pernyataan persetujuan
6. **Submit**: Klik tombol "Kirim"
7. **Simpan ID**: Catat ID 6 digit untuk tracking

### Untuk Admin
1. **Dashboard**: Akses `http://localhost:3000/dashboard/keberatan`
2. **Monitor**: Lihat statistik dan daftar keberatan
3. **Detail**: Klik icon mata untuk melihat detail
4. **Update Status**: Ubah status keberatan
5. **Tracking**: User dapat melacak di `/informasi`

### Tracking
1. **Akses**: `http://localhost:3000/informasi`
2. **Input ID**: Masukkan ID 6 digit di form tracking
3. **Hasil**: Sistem akan menampilkan detail keberatan
4. **Auto-detect**: Sistem otomatis mendeteksi jenis (permohonan/keberatan)

## Database Migration

Untuk menerapkan schema ke database:
```bash
npx prisma db push
npx prisma generate
```

## File Structure

```
app/
├── keberatan/
│   └── page.js                 # Form keberatan
├── dashboard/
│   └── keberatan/
│       └── page.js             # Admin dashboard
├── api/
│   └── keberatan/
│       ├── route.js            # CRUD operations
│       └── [id]/
│           └── route.js        # Individual keberatan operations
├── informasi/
│   └── page.js                 # Updated tracking
└── components/
    └── cardData.jsx            # Updated card

prisma/
└── schema.prisma               # Updated with KeberatanInformasi model

public/
└── uploads/
    └── keberatan/              # File uploads directory
```

## Keamanan

- File upload dengan validasi tipe dan ukuran
- Data disimpan dengan ID 6 digit untuk kemudahan tracking
- Form validation di client dan server side
- Sanitasi input data
- Protected admin endpoints

## Status Workflow

1. **pending** - Keberatan baru diterima
2. **diproses** - Sedang ditinjau admin
3. **selesai** - Keberatan telah diselesaikan
4. **ditolak** - Keberatan ditolak dengan alasan

Sistem keberatan telah siap digunakan dan terintegrasi penuh dengan sistem permohonan informasi yang sudah ada.
