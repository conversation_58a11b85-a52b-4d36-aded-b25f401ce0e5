import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { cookies } from 'next/headers';
import { verifyToken } from '../../../lib/auth';

// GET untuk mendapatkan detail tag
export async function GET(request, props) {
  const params = await props.params;
  try {
    const { id } = params;
    
    const tag = await prisma.tag.findUnique({
      where: { id },
      include: {
        _count: {
          select: { posts: true }
        }
      }
    });
    
    if (!tag) {
      return NextResponse.json(
        { error: 'Tag tidak ditemukan' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({ tag });
  } catch (error) {
    console.error('Error fetching tag:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data tag' },
      { status: 500 }
    );
  }
}

// PUT untuk update tag
export async function PUT(request, props) {
  const params = await props.params;
  try {
    // Verify authentication using JWT token
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'Unauthorized - login required' },
        { status: 401 }
      );
    }
    
    // Verify token and get user data
    const userData = await verifyToken(token);
    if (!userData || !userData.id) {
      return NextResponse.json(
        { error: 'Token tidak valid atau kadaluwarsa' },
        { status: 401 }
      );
    }
      // Only admins can update tags
    if (userData.role && userData.role.toLowerCase() !== 'admin') {
      return NextResponse.json(
        { error: 'Anda tidak memiliki izin untuk mengubah tag' },
        { status: 403 }
      );
    }
    
    const { id } = params;
    const { name, description } = await request.json();
    
    // Validasi input
    if (!name || name.trim() === '') {
      return NextResponse.json(
        { error: 'Nama tag tidak boleh kosong' },
        { status: 400 }
      );
    }
    
    // Generate slug dari nama
    const slug = name.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-');
    
    // Cek apakah tag dengan nama/slug tersebut sudah ada (exclude current tag)
    const existingTag = await prisma.tag.findFirst({
      where: {
        OR: [
          { name },
          { slug }
        ],
        NOT: {
          id
        }
      }
    });
    
    if (existingTag) {
      return NextResponse.json(
        { error: 'Tag dengan nama atau slug tersebut sudah ada' },
        { status: 400 }
      );
    }
    
    // Update tag
    const updatedTag = await prisma.tag.update({
      where: { id },
      data: {
        name,
        slug,
        description
      }
    });
    
    return NextResponse.json({ tag: updatedTag });
  } catch (error) {
    console.error('Error updating tag:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat memperbarui tag' },
      { status: 500 }
    );
  }
}

// DELETE untuk menghapus tag
export async function DELETE(request, props) {
  const params = await props.params;
  try {
    console.log(`API: Attempting to delete tag with ID: ${params.id}`);
    
    const { id } = params;
    
    // Cek jika tag masih digunakan dalam post
    const tagWithPosts = await prisma.tag.findUnique({
      where: { id },
      include: {
        _count: {
          select: { posts: true }
        }
      }
    });
    
    console.log(`API: Tag found with ${tagWithPosts?._count?.posts || 0} posts`);
    
    if (tagWithPosts && tagWithPosts._count.posts > 0) {
      console.log('API: Tag still has posts, cannot delete');
      return NextResponse.json(
        { error: 'Tag masih digunakan dalam postingan dan tidak dapat dihapus' },
        { status: 400 }
      );
    }
    
    // Hapus tag
    console.log('API: Deleting tag from database');
    await prisma.tag.delete({
      where: { id }
    });
    
    console.log('API: Tag deleted successfully');
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('API Error deleting tag:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat menghapus tag: ' + error.message },
      { status: 500 }
    );
  }
}
