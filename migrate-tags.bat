@echo off
echo ==============================================
echo    Enhanced Tags Database Migration Script
echo ==============================================
echo.

set "SCRIPT_DIR=%~dp0"
cd /d "%SCRIPT_DIR%.."

echo [INFO] Current directory: %cd%
echo.

if "%1"=="backup" goto backup
if "%1"=="validate" goto validate
if "%1"=="migrate" goto migrate
if "%1"=="rollback" goto rollback
if "%1"=="help" goto help

:menu
echo Please choose an option:
echo.
echo 1. Backup current data
echo 2. Validate current schema  
echo 3. Run full migration
echo 4. Help / Instructions
echo 5. Exit
echo.
set /p choice="Enter your choice (1-5): "

if "%choice%"=="1" goto backup
if "%choice%"=="2" goto validate
if "%choice%"=="3" goto migrate
if "%choice%"=="4" goto help
if "%choice%"=="5" goto exit
echo Invalid choice. Please try again.
goto menu

:backup
echo.
echo [STEP] Creating backup of current data...
echo.
node prisma\migrate-enhanced-tags.js backup
if %errorlevel% neq 0 (
    echo [ERROR] Backup failed!
    pause
    goto menu
)
echo [SUCCESS] Backup completed!
echo.
pause
goto menu

:validate
echo.
echo [STEP] Validating current schema...
echo.
node prisma\migrate-enhanced-tags.js validate
if %errorlevel% neq 0 (
    echo [ERROR] Validation failed!
    pause
    goto menu
)
echo [SUCCESS] Validation completed!
echo.
pause
goto menu

:migrate
echo.
echo ============================================
echo           MIGRATION WARNING
echo ============================================
echo This will modify your database schema.
echo Make sure you have:
echo 1. Created a backup of your database
echo 2. No active users on the system
echo 3. Tested this on a development environment
echo.
set /p confirm="Are you sure you want to continue? (y/N): "
if /i not "%confirm%"=="y" goto menu

echo.
echo [STEP 1/4] Creating automatic backup...
node prisma\migrate-enhanced-tags.js backup
if %errorlevel% neq 0 (
    echo [ERROR] Backup failed! Aborting migration.
    pause
    goto menu
)

echo.
echo [STEP 2/4] Generating Prisma schema...
npx prisma generate
if %errorlevel% neq 0 (
    echo [ERROR] Prisma generate failed!
    pause
    goto menu
)

echo.
echo [STEP 3/4] Running database migration...
npx prisma migrate dev --name enhanced-tags-features
if %errorlevel% neq 0 (
    echo [ERROR] Migration failed!
    echo [INFO] Check the backup files in prisma\backup\
    pause
    goto menu
)

echo.
echo [STEP 4/4] Updating existing records...
node prisma\migrate-enhanced-tags.js migrate
if %errorlevel% neq 0 (
    echo [ERROR] Data update failed!
    pause
    goto menu
)

echo.
echo ============================================
echo        MIGRATION COMPLETED SUCCESSFULLY!
echo ============================================
echo.
echo What was done:
echo ✓ Backup created in prisma\backup\
echo ✓ Database schema updated
echo ✓ New indexes created for better performance
echo ✓ Existing records updated with default values
echo ✓ New audit fields added (createdBy, updatedBy)
echo ✓ Soft delete capability added (isActive)
echo ✓ Tag prioritization added (priority)
echo ✓ UI enhancement fields added (color, icon)
echo.
echo Next steps:
echo 1. Test the enhanced tags functionality
echo 2. Update your API code to use new fields
echo 3. Update your frontend to utilize new features
echo.
pause
goto menu

:rollback
echo.
echo ============================================
echo              ROLLBACK WARNING
echo ============================================
echo This is a complex operation that may require manual steps.
echo Please check the backup files in prisma\backup\ directory.
echo.
echo For safety, please do this manually:
echo 1. Stop your application
echo 2. Restore from your database backup
echo 3. Use the backup JSON files if needed
echo.
pause
goto menu

:help
echo.
echo ============================================
echo         ENHANCED TAGS MIGRATION HELP
echo ============================================
echo.
echo This script helps you migrate from the basic tags schema
echo to an enhanced schema with the following new features:
echo.
echo NEW FEATURES:
echo ✓ Soft delete capability (isActive field)
echo ✓ Tag prioritization (priority field)  
echo ✓ UI enhancements (color, icon fields)
echo ✓ Audit trails (createdBy, updatedBy fields)
echo ✓ Better indexing for performance
echo ✓ Enhanced relationship tracking
echo.
echo MIGRATION PROCESS:
echo 1. Backup current data automatically
echo 2. Update database schema via Prisma
echo 3. Add new indexes for better performance
echo 4. Update existing records with default values
echo.
echo REQUIREMENTS:
echo ✓ Node.js installed
echo ✓ Prisma CLI available (npx prisma)
echo ✓ Database connection working
echo ✓ No active users during migration
echo.
echo SAFETY MEASURES:
echo ✓ Automatic backup before migration
echo ✓ Schema validation before changes
echo ✓ Rollback scripts available
echo ✓ Step-by-step process with error handling
echo.
pause
goto menu

:exit
echo.
echo Migration script ended.
exit /b 0
