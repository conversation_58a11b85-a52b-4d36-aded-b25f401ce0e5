'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import { Upload, FileText, CheckCircle, AlertCircle, Link as LinkIcon, Trash2, Plus, ChevronLeft as ChevronLeftIcon, ChevronRight as ChevronRightIcon } from 'lucide-react';
import toast from 'react-hot-toast';

const formatDate = (dateString) => {
  if (!dateString) return '-';
  
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '-'; // Check for Invalid Date
    
    return date.toLocaleDateString('id-ID', {
      day: 'numeric',
      month: 'long',
      year: 'numeric'
    });
  } catch (err) {
    console.error('Error formatting date:', err);
    return '-';
  }
};

export default function UploadPage() {
  const router = useRouter();
  const [showUploadForm, setShowUploadForm] = useState(false);
  const [file, setFile] = useState(null);
  const [fileName, setFileName] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('dokumen');
  const [isPublic, setIsPublic] = useState(true);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState(false);
  const [dragActive, setDragActive] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const [fetchingFiles, setFetchingFiles] = useState(true);
  const [copyTooltip, setCopyTooltip] = useState({ visible: false, fileId: null });

  // Tipe file yang diizinkan
  const allowedTypes = ['application/pdf', 'image/jpeg', 'image/png'];
  const fileExtensions = ['.pdf', '.jpeg', '.jpg', '.png'];

  useEffect(() => {
    // Fetch uploaded files
    const fetchFiles = async () => {
      try {
        setFetchingFiles(true);
        const response = await fetch('/api/files');
        const data = await response.json();
        
        if (response.ok) {
          setUploadedFiles(data.files || []);
        }
      } catch (err) {
        console.error('Error fetching files:', err);
      } finally {
        setFetchingFiles(false);
      }
    };

    fetchFiles();
  }, [success]);

  const handleFileChange = (e) => {
    const selectedFile = e.target.files[0];
    validateAndSetFile(selectedFile);
  };

  const validateAndSetFile = (selectedFile) => {
    setError('');
    
    if (!selectedFile) return;
    
    if (!allowedTypes.includes(selectedFile.type)) {
      setError(`Tipe file tidak diizinkan. Hanya file ${fileExtensions.join(', ')} yang diperbolehkan.`);
      return;
    }
    
    if (selectedFile.size > 5 * 1024 * 1024) {
      setError('Ukuran file terlalu besar. Maksimal 5MB.');
      return;
    }
    
    setFile(selectedFile);
    setFileName(selectedFile.name);
  };

  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      validateAndSetFile(e.dataTransfer.files[0]);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!file) {
      toast.error('Silakan pilih file terlebih dahulu.');
      return;
    }
    
    setLoading(true);
    
    try {
      const formData = new FormData();
      formData.append('file', file);
      formData.append('fileName', fileName);
      formData.append('description', description);
      formData.append('category', category);
      formData.append('isPublic', isPublic);
      
      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Terjadi kesalahan saat mengunggah file');
      }
      
      toast.success('File berhasil diunggah!');
      setFile(null);
      setFileName('');
      setDescription('');
      setShowUploadForm(false);
    } catch (err) {
      toast.error(err.message || 'Terjadi kesalahan saat mengunggah file');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteFile = async (fileId) => {
    if (confirm('Apakah Anda yakin ingin menghapus file ini?')) {
      try {
        setLoading(true);
        const response = await fetch(`/api/files/${fileId}`, {
          method: 'DELETE',
        });
        
        if (response.ok) {
          setUploadedFiles(uploadedFiles.filter(file => file.id !== fileId));
        } else {
          throw new Error('Gagal menghapus file');
        }
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
  };

  const copyToClipboard = async (text, fileId) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success('Link berhasil disalin!');
    } catch (err) {
      console.error('Gagal menyalin link:', err);
      toast.error('Gagal menyalin link. Silakan coba lagi.');
    }
  };

  return (
    <div className="flex-1">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="flex items-center justify-between px-4 py-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
          <h1 className="text-xl font-bold text-gray-900">Media</h1>
        </div>
      </header>

      <main className="px-4 py-6 mx-auto max-w-7xl sm:px-6 lg:px-8">
        {/* WordPress-style Media Library */}
        <div className="bg-white rounded-lg shadow">
          {/* Toolbar */}
          <div className="flex items-center justify-between p-4 border-b">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setShowUploadForm(!showUploadForm)}
                className="flex items-center px-3 py-2 text-sm font-medium text-white rounded-md bg-primary-600 hover:bg-primary-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                Tambah Media
              </button>
            </div>
            
            <div className="flex items-center">
              <input
                type="text"
                placeholder="Cari media..."
                className="px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
          </div>
          
          {/* Success/Error Messages */}
          {success && (
            <div className="flex items-center p-4 m-4 text-green-700 bg-green-100 rounded-md">
              <CheckCircle className="mr-2" size={20} />
              <span>File berhasil diunggah!</span>
            </div>
          )}
          
          {error && (
            <div className="flex items-center p-4 m-4 text-red-700 bg-red-100 rounded-md">
              <AlertCircle className="mr-2" size={20} />
              <span>{error}</span>
            </div>
          )}
          
          {/* Upload Form (Collapsible) */}
          {showUploadForm && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="p-4 border-b"
            >
              <form onSubmit={handleSubmit} className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div 
                  className={`border-2 border-dashed rounded-lg p-6 text-center col-span-2 ${
                    dragActive ? 'border-primary-500 bg-primary-50' : 'border-gray-300'
                  }`}
                  onDragEnter={handleDrag}
                  onDragLeave={handleDrag}
                  onDragOver={handleDrag}
                  onDrop={handleDrop}
                >
                  <input
                    type="file"
                    id="file-upload"
                    onChange={handleFileChange}
                    accept=".pdf,.jpeg,.jpg,.png"
                    className="hidden"
                  />
                  
                  {file ? (
                    <div className="flex flex-col items-center">
                      <FileText size={40} className="mb-2 text-primary-500" />
                      <p className="mb-1 font-medium">{file.name}</p>
                      <p className="text-sm text-gray-500">
                        {(file.size / 1024 / 1024).toFixed(2)} MB
                      </p>
                      <button
                        type="button"
                        onClick={() => {
                          setFile(null);
                          setFileName('');
                        }}
                        className="mt-2 text-sm text-red-600 hover:text-red-800"
                      >
                        Hapus
                      </button>
                    </div>
                  ) : (
                    <div className="flex flex-col items-center">
                      <Upload size={40} className="mb-2 text-gray-400" />
                      <p className="mb-2 text-sm text-gray-500">
                        Drag & drop file di sini, atau
                      </p>
                      <label
                        htmlFor="file-upload"
                        className="px-4 py-2 text-sm font-medium text-white rounded-md cursor-pointer bg-primary-600 hover:bg-primary-700"
                      >
                        Pilih File
                      </label>
                      <p className="mt-2 text-xs text-gray-500">
                        Format yang didukung: PDF, JPEG, PNG (Maks. 5MB)
                      </p>
                    </div>
                  )}
                </div>
                
                <div>
                  <label htmlFor="fileName" className="block mb-1 text-sm font-medium text-gray-700">
                    Nama File
                  </label>
                  <input
                    type="text"
                    id="fileName"
                    value={fileName}
                    onChange={(e) => setFileName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="category" className="block mb-1 text-sm font-medium text-gray-700">
                    Kategori
                  </label>
                  <select
                    id="category"
                    value={category}
                    onChange={(e) => setCategory(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                  >
                    <option value="dokumen">Dokumen</option>
                    <option value="gambar">Gambar</option>
                    <option value="laporan">Laporan</option>
                    <option value="lainnya">Lainnya</option>
                  </select>
                </div>
                
                <div className="col-span-2">
                  <label htmlFor="description" className="block mb-1 text-sm font-medium text-gray-700">
                    Deskripsi
                  </label>
                  <textarea
                    id="description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
                    rows="2"
                  />
                </div>
                
                <div className="col-span-2">
                  <label className="flex items-center">
                    <input
                      type="checkbox"
                      checked={isPublic}
                      onChange={(e) => setIsPublic(e.target.checked)}
                      className="w-4 h-4 border-gray-300 rounded text-primary-600 focus:ring-primary-500"
                    />
                    <span className="ml-2 text-sm text-gray-700">File dapat diakses publik</span>
                  </label>
                </div>
                
                <div className="flex justify-end col-span-2 space-x-2">
                  <button
                    type="button"
                    onClick={() => setShowUploadForm(false)}
                    className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
                  >
                    Batal
                  </button>
                  <button
                    type="submit"
                    disabled={loading || !file}
                    className={`px-4 py-2 text-sm font-medium text-white rounded-md ${
                      loading || !file
                        ? 'bg-primary-400 cursor-not-allowed'
                        : 'bg-primary-600 hover:bg-primary-700'
                    }`}
                  >
                    {loading ? 'Mengunggah...' : 'Upload'}
                  </button>
                </div>
              </form>
            </motion.div>
          )}
          
          {/* Files Table */}
          {fetchingFiles ? (
            <div className="flex justify-center py-8">
              <div className="w-8 h-8 border-4 rounded-full border-primary-500 border-t-transparent animate-spin"></div>
            </div>
          ) : uploadedFiles.length === 0 ? (
            <div className="py-12 text-center text-gray-500">
              <FileText size={48} className="mx-auto mb-2 text-gray-400" />
              <p className="text-lg">Belum ada file yang diupload</p>
              <p className="mt-2 text-sm">Klik "Tambah Media" untuk mengunggah file baru</p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">File</th>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Kategori</th>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Status</th>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Tanggal</th>
                    <th scope="col" className="px-6 py-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">Aksi</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {uploadedFiles.map((file) => (
                    <tr key={file.id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <FileText className="flex-shrink-0 w-5 h-5 mr-3 text-gray-400" />
                          <div className="max-w-xs truncate">
                            <div className="font-medium text-gray-900">{file.name}</div>
                            <div className="text-sm text-gray-500 truncate">{file.description}</div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="inline-flex px-2 text-xs font-semibold leading-5 text-green-800 bg-green-100 rounded-full">
                          {file.category}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex px-2 text-xs font-semibold leading-5 rounded-full ${
                          file.isPublic 
                            ? 'bg-green-100 text-green-800' 
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {file.isPublic ? 'Publik' : 'Private'}
                        </span>
                      </td>
                      <td className="px-6 py-4 text-sm text-gray-500 whitespace-nowrap">
                        {formatDate(file.createdAt)}
                      </td>
                      <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                        <div className="flex space-x-3">
                          <div className="relative">
                            <button 
                              onClick={(e) => {
                                e.preventDefault();
                                copyToClipboard(`${window.location.origin}${file.path}`, file.id);
                              }}
                              className="text-primary-600 hover:text-primary-900"
                              title="Salin link"
                            >
                              <LinkIcon className="w-5 h-5" />
                            </button>
                            {copyTooltip.visible && copyTooltip.fileId === file.id && (
                              <div className="absolute px-2 py-1 mb-1 text-xs text-white transform -translate-x-1/2 bg-gray-800 rounded shadow bottom-full left-1/2">
                                Link disalin!
                              </div>
                            )}
                          </div>
                          <button
                            onClick={() => handleDeleteFile(file.id)}
                            className="text-red-600 hover:text-red-900"
                          >
                            <Trash2 className="w-5 h-5" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          
          {/* Pagination */}
          <div className="flex items-center justify-between px-4 py-3 bg-white border-t border-gray-200 sm:px-6">
            <div className="flex justify-between flex-1 sm:hidden">
              <button className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                Previous
              </button>
              
            </div>
            <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p className="text-sm text-gray-700">
                  Showing
                  <span className="font-medium">1</span>
                  to
                  <span className="font-medium">10</span>
                  of
                  <span className="font-medium">20</span>
                  results
                </p>
              </div>
              <div>
                <nav className="relative z-0 inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                  <a href="#" className="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-l-md hover:bg-gray-50 focus:z-10">
                    <span className="sr-only">Previous</span>
                    <ChevronLeftIcon className="w-5 h-5" aria-hidden="true" />
                  </a>
                  <a href="#" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10">
                    1
                  </a>
                  <a href="#" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10">
                    2
                  </a>
                  <a href="#" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10">
                    3
                  </a>
                  <a href="#" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10">
                    4
                  </a>
                  <a href="#" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10">
                    5
                  </a>
                  <span className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300">
                    ...
                  </span>
                  <a href="#" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10">
                    8
                  </a>
                  <a href="#" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10">
                    9
                  </a>
                  <a href="#" className="relative inline-flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:z-10">
                    10
                  </a>
                  <a href="#" className="relative inline-flex items-center px-2 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-r-md hover:bg-gray-50 focus:z-10">
                    <span className="sr-only">Next</span>
                    <ChevronRightIcon className="w-5 h-5" aria-hidden="true" />
                  </a>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}



