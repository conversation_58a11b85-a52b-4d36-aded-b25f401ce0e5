'use client';

import { useAuth } from '../context/AuthContext';

export default function DebugPage() {
  const authData = useAuth();
  
  return (
    <div className="p-8">
      <h1 className="mb-4 text-2xl font-bold">Debug Dashboard</h1>
      <div className="p-4 bg-gray-100 rounded">
        <pre>{JSON.stringify(authData, null, 2)}</pre>
      </div>
      <div className="mt-4">
        <p>User: {authData?.user ? 'Logged in' : 'Not logged in'}</p>
        <p>Loading: {authData?.loading ? 'Yes' : 'No'}</p>
      </div>
    </div>
  );
}
