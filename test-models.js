const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

console.log('Testing Prisma models...');
console.log('Available models:', Object.keys(prisma).filter(key => !key.startsWith('$')));

// Test specific models
if (prisma.refreshtoken) {
    console.log('✅ refreshtoken model exists (lowercase)');
} else {
    console.log('❌ refreshtoken model not found');
}

if (prisma.refreshToken) {
    console.log('✅ refreshToken model exists (camelCase)');
} else {
    console.log('❌ refreshToken model not found');
}
