// components/InformasiSertaMerta.js
'use client'
import Link from 'next/link'

export default function InformasiSertaMerta() {
  const informasiLinks = [
    {
      title: "Diskresi Surat Keputusan Bersama Empat Menteri tentang Pembelajaran di Masa Pandemi Covid-19",
      url: "https://www.kemdikbud.go.id/main/blog/2022/07/se-mendikbudristek-no-7-th-2022-ttg-diskresi-skb-4-menteri-ttg-pembelajaran-di-masa-pandemi-covid19" // Ganti dengan URL yang sesuai
    },
    {
      title: "Satuan Pendidikan Aman Bencana",
      url: "https://spab.kemdikbud.go.id/" // Ganti dengan URL yang sesuai
    },
    
    {
      title: "Sekolah Penggerak",
      url: "https://sekolah.penggerak.kemdikbud.go.id/gurupenggerak/" // Ganti dengan URL yang sesuai
    }
  ]

  return (
    <div className="w-full px-4 pt-16">
      <div className="w-full max-w-4xl p-6 mx-auto bg-white rounded-2xl">
        <h2 className="mb-6 text-2xl font-bold text-gray-800">Informasi Serta Merta</h2>
        <ul className="space-y-4">
          {informasiLinks.map((item, index) => (
            <li key={index}>
              <Link href={item.url} className="text-blue-600 hover:text-blue-800 hover:underline">
                {item.title}
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  )
}