// Test script to verify component imports work correctly
const path = require('path');

console.log('Testing component imports...');

try {
  // Test if we can require the components (this simulates the import process)
  const enhancedTablePath = path.join(__dirname, 'app', 'components', 'EnhancedTagsTable.jsx');
  const modalPath = path.join(__dirname, 'app', 'components', 'TagFormModal.jsx');
  const pagePath = path.join(__dirname, 'app', 'dashboard', 'tags', 'page.js');
  
  console.log('✓ EnhancedTagsTable path:', enhancedTablePath);
  console.log('✓ TagFormModal path:', modalPath);
  console.log('✓ Tags page path:', pagePath);
  
  // Check if files exist
  const fs = require('fs');
  
  if (fs.existsSync(enhancedTablePath)) {
    console.log('✓ EnhancedTagsTable.jsx exists');
    const content = fs.readFileSync(enhancedTablePath, 'utf8');
    if (content.includes('export default EnhancedTagsTable')) {
      console.log('✓ EnhancedTagsTable has default export');
    } else {
      console.log('✗ EnhancedTagsTable missing default export');
    }
  }
  
  if (fs.existsSync(modalPath)) {
    console.log('✓ TagFormModal.jsx exists');
    const content = fs.readFileSync(modalPath, 'utf8');
    if (content.includes('export default TagFormModal')) {
      console.log('✓ TagFormModal has default export');
    } else {
      console.log('✗ TagFormModal missing default export');
    }
  }
  
  if (fs.existsSync(pagePath)) {
    console.log('✓ page.js exists');
    const content = fs.readFileSync(pagePath, 'utf8');
    if (content.includes('import EnhancedTagsTable from') && content.includes('import TagFormModal from')) {
      console.log('✓ page.js imports both components correctly');
    } else {
      console.log('✗ page.js has import issues');
    }
  }
  
  console.log('\n✓ All component export/import issues should be resolved!');
  
} catch (error) {
  console.error('✗ Error testing components:', error.message);
}
