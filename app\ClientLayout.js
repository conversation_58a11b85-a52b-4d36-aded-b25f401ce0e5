'use client';

import ErrorBoundary from './components/ErrorBoundary';
import { Toaster } from 'react-hot-toast';
import { useEffect } from 'react';
import { usePathname } from 'next/navigation';

// Fungsi untuk menangani analitik sederhana dengan defer
function logPageView(path) {
  // Gunakan setTimeout untuk menunda tracking agar tidak mengganggu rendering
  setTimeout(() => {
    try {
      // Implementasi sederhana untuk tracking pageview
      // Bisa diganti dengan sistem analitik seperti Google Analytics, Matomo, dll.
      // console.log dinonaktifkan
    } catch (error) {
      // Error handling
    }
  }, 100);
}

export default function ClientLayout({ children }) {
  const pathname = usePathname();
  
  // Track page view saat pathname berubah
  useEffect(() => {
    if (pathname) {
      logPageView(pathname);
    }
  }, [pathname]);
  return (
    <ErrorBoundary fallback={<FallbackUI />}>
      <>
        {children}
        <Toaster 
          position="top-right"
          toastOptions={{
            duration: 4000,
            className: 'toast-container',
            style: {
              background: '#fff',
              color: '#333',
              boxShadow: '0 4px 6px rgba(0,0,0,0.1)',
              borderRadius: '0.5rem',
              padding: '0.75rem 1rem',
            },
            success: {
              style: {
                border: '1px solid #10B981',
              },
              iconTheme: {
                primary: '#10B981',
                secondary: '#fff',
              },
            },
            error: {
              style: {
                border: '1px solid #EF4444',
              },
              iconTheme: {
                primary: '#EF4444',
                secondary: '#fff',
              },
            },
          }}
        />
      </>
    </ErrorBoundary>
  );
}

// Component UI fallback untuk error boundary
function FallbackUI() {
  return (
    <div className="flex items-center justify-center min-h-screen px-4 bg-gray-50">
      <div className="max-w-md p-6 text-center bg-white rounded-lg shadow-lg">
        <h2 className="mb-4 text-2xl font-bold text-red-600">Oops! Terjadi Kesalahan</h2>
        <p className="mb-4 text-gray-600">
          Mohon maaf, terjadi kesalahan saat memuat halaman. Silakan muat ulang halaman atau hubungi administrator.
        </p>
        <button
          onClick={() => window.location.reload()}
          className="px-4 py-2 text-white bg-blue-600 rounded hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
        >
          Muat Ulang Halaman
        </button>
      </div>
    </div>
  );
}
