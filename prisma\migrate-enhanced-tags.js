#!/usr/bin/env node
/**
 * Enhanced Tags Migration Script
 * Script untuk melakukan migrasi database dengan aman ke enhanced tags schema
 */

const { PrismaClient } = require('@prisma/client');
const prisma = new PrismaClient();

async function backupExistingData() {
  console.log('🔄 Creating backup of existing data...');
  
  try {
    // Backup existing tags
    const existingTags = await prisma.tag.findMany();
    const existingTagsOnPosts = await prisma.tagsOnPosts.findMany();
    
    console.log(`📊 Found ${existingTags.length} existing tags`);
    console.log(`📊 Found ${existingTagsOnPosts.length} existing tag-post relations`);
    
    // Save to backup files
    const fs = require('fs');
    const path = require('path');
    
    const backupDir = path.join(__dirname, 'backup');
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true });
    }
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    fs.writeFileSync(
      path.join(backupDir, `tags-backup-${timestamp}.json`),
      JSON.stringify(existingTags, null, 2)
    );
    
    fs.writeFileSync(
      path.join(backupDir, `tags-on-posts-backup-${timestamp}.json`),
      JSON.stringify(existingTagsOnPosts, null, 2)
    );
    
    console.log('✅ Backup completed successfully');
    return { tags: existingTags, tagsOnPosts: existingTagsOnPosts };
  } catch (error) {
    console.error('❌ Error creating backup:', error);
    throw error;
  }
}

async function validateCurrentSchema() {
  console.log('🔍 Validating current schema...');
  
  try {
    // Check if new columns already exist
    const result = await prisma.$queryRaw`
      SELECT COLUMN_NAME 
      FROM INFORMATION_SCHEMA.COLUMNS 
      WHERE TABLE_NAME = 'tags' AND TABLE_SCHEMA = DATABASE()
    `;
    
    const columns = result.map(row => row.COLUMN_NAME);
    console.log('📋 Current tag table columns:', columns);
    
    const hasNewColumns = ['isActive', 'priority', 'color', 'icon', 'createdBy', 'updatedBy']
      .some(col => columns.includes(col));
    
    return { hasNewColumns, columns };
  } catch (error) {
    console.error('❌ Error validating schema:', error);
    throw error;
  }
}

async function runMigration() {
  console.log('🚀 Starting Enhanced Tags Migration...');
  
  try {
    // Step 1: Backup existing data
    const backup = await backupExistingData();
    
    // Step 2: Validate schema
    const { hasNewColumns } = await validateCurrentSchema();
    
    if (hasNewColumns) {
      console.log('⚠️  New columns already exist. Skipping schema migration.');
    } else {
      console.log('🔄 Running Prisma migration...');
      
      // Step 3: Run Prisma migration
      const { exec } = require('child_process');
      const { promisify } = require('util');
      const execAsync = promisify(exec);
      
      await execAsync('npx prisma migrate dev --name enhanced-tags-schema');
      console.log('✅ Prisma migration completed');
    }
    
    // Step 4: Update existing records with default values
    console.log('🔄 Updating existing records...');
    
    const updateResult = await prisma.tag.updateMany({
      where: {
        OR: [
          { isActive: null },
          { priority: null }
        ]
      },
      data: {
        isActive: true,
        priority: 0
      }
    });
    
    console.log(`✅ Updated ${updateResult.count} existing tag records`);
    
    // Step 5: Add addedAt values to existing TagsOnPosts if needed
    const tagsOnPostsUpdate = await prisma.tagsOnPosts.updateMany({
      where: {
        addedAt: null
      },
      data: {
        addedAt: new Date()
      }
    });
    
    console.log(`✅ Updated ${tagsOnPostsUpdate.count} existing tag-post relations`);
    
    // Step 6: Verify migration
    const updatedTags = await prisma.tag.findMany({
      select: {
        id: true,
        name: true,
        isActive: true,
        priority: true,
        createdAt: true
      },
      take: 5
    });
    
    console.log('📊 Sample of updated tags:', updatedTags);
    
    console.log('🎉 Migration completed successfully!');
    
    return {
      success: true,
      backupCreated: true,
      recordsUpdated: updateResult.count,
      relationsUpdated: tagsOnPostsUpdate.count
    };
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.log('💡 Please check the backup files and consider manual rollback if needed.');
    throw error;
  }
}

async function rollbackMigration() {
  console.log('🔄 Starting rollback process...');
  
  try {
    const fs = require('fs');
    const path = require('path');
    
    // Find latest backup
    const backupDir = path.join(__dirname, 'backup');
    const backupFiles = fs.readdirSync(backupDir)
      .filter(file => file.startsWith('tags-backup-'))
      .sort()
      .reverse();
    
    if (backupFiles.length === 0) {
      throw new Error('No backup files found');
    }
    
    const latestBackup = backupFiles[0];
    console.log(`📂 Using backup: ${latestBackup}`);
    
    const backupData = JSON.parse(
      fs.readFileSync(path.join(backupDir, latestBackup), 'utf8')
    );
    
    // This is a simplified rollback - in production you might want more sophisticated logic
    console.log('⚠️  Rollback requires manual intervention. Backup data available at:');
    console.log(`   ${path.join(backupDir, latestBackup)}`);
    
  } catch (error) {
    console.error('❌ Rollback failed:', error);
    throw error;
  }
}

// Main execution
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  try {
    switch (command) {
      case 'migrate':
        await runMigration();
        break;
      case 'rollback':
        await rollbackMigration();
        break;
      case 'backup':
        await backupExistingData();
        break;
      case 'validate':
        await validateCurrentSchema();
        break;
      default:
        console.log('Usage: node migrate-enhanced-tags.js [migrate|rollback|backup|validate]');
        console.log('');
        console.log('Commands:');
        console.log('  migrate  - Run full migration to enhanced tags schema');
        console.log('  rollback - Rollback to previous schema (requires manual steps)');
        console.log('  backup   - Create backup of current data only');
        console.log('  validate - Check current schema state');
    }
  } catch (error) {
    console.error('❌ Command failed:', error);
    process.exit(1);
  } finally {
    await prisma.$disconnect();
  }
}

if (require.main === module) {
  main();
}

module.exports = {
  runMigration,
  rollbackMigration,
  backupExistingData,
  validateCurrentSchema
};
