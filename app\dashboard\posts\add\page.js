'use client';

import { useState, useEffect, useId } from 'react';
import { useRouter } from 'next/navigation';
import dynamic from 'next/dynamic';
import DashboardLayout from '../../../components/DashboardLayout';

// Dynamically import TinyMCE component with SSR disabled and increased height
const TinyMCEComponent = dynamic(
  () => import('../../../components/TinyMCEWrapper'),
  { 
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center p-4 border border-gray-300 rounded-md bg-gray-50 min-h-[600px]">
        <div className="w-6 h-6 mr-2 border-2 border-t-2 border-gray-500 rounded-full border-t-primary-600 animate-spin"></div>
        <span className="text-sm text-gray-500">Loading editor...</span>
      </div>
    )
  }
);

export default function AddPostPage() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    featuredImage: '',
    published: false,
    tags: []
  });
  const [availableTags, setAvailableTags] = useState([]);
  const [selectedTags, setSelectedTags] = useState('');
  
  // Generate a stable unique ID for the editor
  const editorId = useId();

  // Tambahkan state terpisah untuk content
  const [editorContent, setEditorContent] = useState('');

  // Fetch tags when component mounts
  useEffect(() => {
    async function fetchTags() {
      try {
        const response = await fetch('/api/tags');
        if (!response.ok) {
          throw new Error('Failed to fetch tags');
        }
        const data = await response.json();
        // Pastikan kita mengambil array tags dari response
        setAvailableTags(data.tags || []);
      } catch (error) {
        console.error('Error fetching tags:', error);
        setAvailableTags([]); // Set empty array jika terjadi error
      }
    }
    
    fetchTags();
  }, []);

  const handleChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  // Gunakan handleEditorChange yang terpisah
  const handleEditorChange = (content) => {
    setEditorContent(content);
  };

  const handleTagChange = (e) => {
    const tagId = e.target.value;
    setSelectedTags(tagId);
  };

  const generateSlug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
      .trim();
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);

    try {
      // Prepare data for submission
      const postData = {
        ...formData,
        content: editorContent,
        tags: selectedTags ? [selectedTags] : [],
        publishedAt: formData.published ? new Date().toISOString() : null
      };

      // Send data to API
      const response = await fetch('/api/posts', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(postData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Gagal menyimpan postingan');
      }

      // Show success message
      alert('Postingan berhasil ditambahkan!');
      
      // Redirect back to posts list
      router.push('/dashboard/posts');
    } catch (error) {
      console.error('Error adding post:', error);
      alert('Terjadi kesalahan saat menambahkan postingan: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6">
      <div className="flex items-center justify-between mb-6">
        <h1 className="text-2xl font-bold">Tambah Postingan Baru</h1>
        <button
          onClick={() => router.back()}
          className="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
        >
          Kembali
        </button>
      </div>
      
      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 gap-6">
          <div className="space-y-4">
            <div>
              <label htmlFor="title" className="block mb-2 text-sm font-medium text-gray-700">
                Judul
              </label>
              <input
                type="text"
                id="title"
                name="title"
                value={formData.title}
                onChange={handleChange}
                onBlur={() => {
                  if (!formData.slug || formData.slug === generateSlug(formData.title)) {
                    setFormData({
                      ...formData,
                      slug: generateSlug(formData.title)
                    });
                  }
                }}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            
            <div>
              <label htmlFor="slug" className="block mb-2 text-sm font-medium text-gray-700">
                Slug
              </label>
              <input
                type="text"
                id="slug"
                name="slug"
                value={formData.slug}
                onChange={handleChange}
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            
            <div>
              <label htmlFor="excerpt" className="block mb-2 text-sm font-medium text-gray-700">
                Ringkasan
              </label>
              <textarea
                id="excerpt"
                name="excerpt"
                value={formData.excerpt}
                onChange={handleChange}
                rows="3"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              ></textarea>
            </div>
            
            <div>
              <label htmlFor="featuredImage" className="block mb-2 text-sm font-medium text-gray-700">
                URL Gambar Utama
              </label>
              <input
                type="url"
                id="featuredImage"
                name="featuredImage"
                value={formData.featuredImage}
                onChange={handleChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              />
            </div>
            
            <div>
              <label htmlFor="tags" className="block mb-2 text-sm font-medium text-gray-700">
                Tag
              </label>
              <select
                id="tags"
                name="tags"
                value={selectedTags}
                onChange={handleTagChange}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
              >
                <option value="">Pilih Tag</option>
                {Array.isArray(availableTags) && availableTags.length > 0 ? (
                  availableTags.map(tag => (
                    <option key={tag.id} value={tag.id}>
                      {tag.name}
                    </option>
                  ))
                ) : (
                  <option value="" disabled>Tidak ada tag tersedia</option>
                )}
              </select>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="published"
                name="published"
                checked={formData.published}
                onChange={handleChange}
                className="w-4 h-4 border-gray-300 rounded text-primary-600 focus:ring-primary-500"
              />
              <label htmlFor="published" className="ml-2 text-sm text-gray-700">
                Publikasikan sekarang
              </label>
            </div>
          </div>
          
          <div>
            <label htmlFor="content" className="block mb-2 text-sm font-medium text-gray-700">
              Konten
            </label>
           <TinyMCEComponent 
  data={formData.content} 
  onChange={handleEditorChange} 
  config={{
    height: 700
  }}
/>
          </div>
        </div>
        
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={loading}
            className="px-4 py-2 text-sm font-medium text-white rounded-md bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 disabled:opacity-50"
          >
            {loading ? 'Menyimpan...' : 'Simpan Postingan'}
          </button>
        </div>
      </form>
    </div>
  );
}





