// Enhanced Tags Testing Suite
// Run this in the browser console at /dashboard/tags

class TagsTestSuite {
  constructor() {
    this.results = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  log(message, status = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const prefix = status === 'pass' ? '✅' : status === 'fail' ? '❌' : 'ℹ️';
    console.log(`${prefix} [${timestamp}] ${message}`);
    
    if (status === 'pass') this.results.passed++;
    if (status === 'fail') this.results.failed++;
    if (status !== 'info') this.results.total++;
    
    this.results.details.push({ timestamp, message, status });
  }

  async wait(ms = 100) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Test 1: Check if enhanced components are properly loaded
  async testComponentsLoaded() {
    this.log('Testing if enhanced components are loaded...', 'info');
    
    const tests = [
      { name: 'Search input', selector: 'input[placeholder*="<PERSON><PERSON>"]' },
      { name: 'Add button', selector: 'button[aria-label*="Tambah tag"]' },
      { name: 'Table headers', selector: 'th[role="columnheader"]' },
      { name: 'Pagination controls', selector: '[role="navigation"]' },
      { name: 'Loading indicator', selector: '.animate-spin' },
      { name: 'Keyboard shortcut info', selector: '.bg-blue-50' }
    ];

    for (const test of tests) {
      const element = document.querySelector(test.selector);
      if (element) {
        this.log(`${test.name} found`, 'pass');
      } else {
        this.log(`${test.name} not found`, 'fail');
      }
    }
  }

  // Test 2: Test keyboard shortcuts
  async testKeyboardShortcuts() {
    this.log('Testing keyboard shortcuts...', 'info');
    
    // Test Ctrl+N
    const ctrlNEvent = new KeyboardEvent('keydown', {
      key: 'n',
      ctrlKey: true,
      bubbles: true,
      cancelable: true
    });
    
    document.dispatchEvent(ctrlNEvent);
    await this.wait(200);
    
    const modal = document.querySelector('[role="dialog"]');
    if (modal) {
      this.log('Ctrl+N shortcut opens modal', 'pass');
      
      // Test ESC key
      const escEvent = new KeyboardEvent('keydown', {
        key: 'Escape',
        bubbles: true,
        cancelable: true
      });
      
      document.dispatchEvent(escEvent);
      await this.wait(200);
      
      const modalAfterEsc = document.querySelector('[role="dialog"]');
      if (!modalAfterEsc) {
        this.log('ESC key closes modal', 'pass');
      } else {
        this.log('ESC key does not close modal', 'fail');
      }
    } else {
      this.log('Ctrl+N shortcut does not open modal', 'fail');
    }
  }

  // Test 3: Test search functionality
  async testSearchFunctionality() {
    this.log('Testing search functionality...', 'info');
    
    const searchInput = document.querySelector('input[placeholder*="Cari"]');
    if (searchInput) {
      const originalRowCount = document.querySelectorAll('tbody tr').length;
      
      // Test search
      searchInput.value = 'nonexistenttagname12345';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      await this.wait(300);
      
      const filteredRowCount = document.querySelectorAll('tbody tr').length;
      
      if (filteredRowCount <= originalRowCount) {
        this.log('Search filtering works', 'pass');
      } else {
        this.log('Search filtering not working properly', 'fail');
      }
      
      // Clear search
      searchInput.value = '';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      await this.wait(300);
    } else {
      this.log('Search input not found', 'fail');
    }
  }

  // Test 4: Test sorting functionality
  async testSortFunctionality() {
    this.log('Testing sort functionality...', 'info');
    
    const sortButtons = document.querySelectorAll('th[role="columnheader"] button');
    if (sortButtons.length > 0) {
      const firstSortButton = sortButtons[0];
      const beforeText = Array.from(document.querySelectorAll('tbody tr td:first-child'))
        .map(cell => cell.textContent.trim())
        .filter(text => text !== '');
      
      firstSortButton.click();
      await this.wait(200);
      
      const afterText = Array.from(document.querySelectorAll('tbody tr td:first-child'))
        .map(cell => cell.textContent.trim())
        .filter(text => text !== '');
      
      const isChanged = JSON.stringify(beforeText) !== JSON.stringify(afterText);
      
      if (isChanged || beforeText.length === 0) {
        this.log('Table sorting works', 'pass');
      } else {
        this.log('Table sorting not working', 'fail');
      }
    } else {
      this.log('Sort buttons not found', 'fail');
    }
  }

  // Test 5: Test form validation
  async testFormValidation() {
    this.log('Testing form validation...', 'info');
    
    const addButton = document.querySelector('button[aria-label*="Tambah tag"]');
    if (addButton) {
      addButton.click();
      await this.wait(200);
      
      const modal = document.querySelector('[role="dialog"]');
      if (modal) {
        const nameInput = modal.querySelector('input[name="name"]');
        const submitButton = modal.querySelector('button[type="submit"]');
        
        if (nameInput && submitButton) {
          // Test empty submission
          nameInput.value = '';
          submitButton.click();
          
          await this.wait(300);
          
          const errorMessage = modal.querySelector('.text-red-500, .text-red-600, [role="alert"]');
          
          if (errorMessage) {
            this.log('Form validation shows error for empty input', 'pass');
          } else {
            this.log('Form validation not working for empty input', 'fail');
          }
          
          // Close modal
          const closeButton = modal.querySelector('button[aria-label*="Close"], button[aria-label*="Tutup"]') 
                            || modal.querySelector('button[type="button"]');
          if (closeButton) closeButton.click();
        } else {
          this.log('Form elements not found in modal', 'fail');
        }
      } else {
        this.log('Modal not opened for form validation test', 'fail');
      }
    } else {
      this.log('Add button not found', 'fail');
    }
  }

  // Test 6: Test accessibility features
  async testAccessibilityFeatures() {
    this.log('Testing accessibility features...', 'info');
    
    const tests = [
      { name: 'ARIA labels', selector: '[aria-label]', minCount: 3 },
      { name: 'ARIA descriptions', selector: '[aria-describedby]', minCount: 0 },
      { name: 'Role attributes', selector: '[role]', minCount: 2 },
      { name: 'Focusable elements', selector: 'button, input, select, textarea, [tabindex]', minCount: 2 },
      { name: 'Semantic headings', selector: 'h1, h2, h3, h4, h5, h6', minCount: 1 }
    ];

    for (const test of tests) {
      const elements = document.querySelectorAll(test.selector);
      if (elements.length >= test.minCount) {
        this.log(`${test.name}: ${elements.length} found`, 'pass');
      } else {
        this.log(`${test.name}: only ${elements.length} found, expected at least ${test.minCount}`, 'fail');
      }
    }

    // Test focus management
    const firstButton = document.querySelector('button');
    if (firstButton) {
      firstButton.focus();
      if (document.activeElement === firstButton) {
        this.log('Focus management works', 'pass');
      } else {
        this.log('Focus management not working', 'fail');
      }
    }
  }

  // Test 7: Test API integration
  async testAPIIntegration() {
    this.log('Testing API integration...', 'info');
    
    try {
      // Test GET /api/tags
      const response = await fetch('/api/tags', {
        credentials: 'include',
        cache: 'no-cache'
      });
      
      if (response.ok) {
        const data = await response.json();
        this.log(`API GET /api/tags works (${data.tags?.length || 0} tags)`, 'pass');
      } else {
        this.log(`API GET /api/tags failed with status ${response.status}`, 'fail');
      }
      
      // Test validation endpoint with invalid data
      const testResponse = await fetch('/api/tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ name: '' })
      });
      
      if (testResponse.status === 400) {
        this.log('API validation works (rejects empty name)', 'pass');
      } else {
        this.log('API validation not working properly', 'fail');
      }
      
    } catch (error) {
      this.log(`API test failed: ${error.message}`, 'fail');
    }
  }

  // Test 8: Test performance
  async testPerformance() {
    this.log('Testing performance...', 'info');
    
    const startTime = performance.now();
    
    // Test search performance
    const searchInput = document.querySelector('input[placeholder*="Cari"]');
    if (searchInput) {
      const searchStartTime = performance.now();
      
      searchInput.value = 'performance test';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
      
      await this.wait(50);
      
      const searchTime = performance.now() - searchStartTime;
      
      if (searchTime < 500) {
        this.log(`Search response time: ${searchTime.toFixed(2)}ms`, 'pass');
      } else {
        this.log(`Search too slow: ${searchTime.toFixed(2)}ms`, 'fail');
      }
      
      // Clear search
      searchInput.value = '';
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    }
    
    // Test DOM update performance
    const updateStartTime = performance.now();
    const button = document.querySelector('button');
    if (button) {
      button.click();
      const updateTime = performance.now() - updateStartTime;
      
      if (updateTime < 100) {
        this.log(`DOM update time: ${updateTime.toFixed(2)}ms`, 'pass');
      } else {
        this.log(`DOM update slow: ${updateTime.toFixed(2)}ms`, 'fail');
      }
    }
  }

  // Run all tests
  async runAllTests() {
    this.log('🚀 Starting Enhanced Tags Dashboard Test Suite', 'info');
    this.log(`Testing at: ${window.location.href}`, 'info');
    
    const tests = [
      'testComponentsLoaded',
      'testKeyboardShortcuts', 
      'testSearchFunctionality',
      'testSortFunctionality',
      'testFormValidation',
      'testAccessibilityFeatures',
      'testAPIIntegration',
      'testPerformance'
    ];

    for (const testName of tests) {
      this.log(`\n--- Running ${testName} ---`, 'info');
      try {
        await this[testName]();
        await this.wait(500); // Pause between tests
      } catch (error) {
        this.log(`Test ${testName} failed with error: ${error.message}`, 'fail');
      }
    }

    this.printResults();
  }

  printResults() {
    console.log('\n' + '='.repeat(60));
    console.log('🎯 ENHANCED TAGS DASHBOARD TEST RESULTS');
    console.log('='.repeat(60));
    console.log(`✅ Passed: ${this.results.passed}`);
    console.log(`❌ Failed: ${this.results.failed}`);
    console.log(`📊 Total: ${this.results.total}`);
    console.log(`📈 Success Rate: ${((this.results.passed / this.results.total) * 100).toFixed(1)}%`);
    console.log('='.repeat(60));
    
    if (this.results.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.results.details
        .filter(d => d.status === 'fail')
        .forEach(d => console.log(`   • ${d.message}`));
    }
    
    console.log('\n📋 Manual Testing Checklist:');
    console.log('   □ Create a new tag using Ctrl+N');
    console.log('   □ Edit an existing tag');
    console.log('   □ Delete a tag (confirm popup)');
    console.log('   □ Search for tags by name');
    console.log('   □ Sort table by clicking headers');
    console.log('   □ Navigate with Tab/Shift+Tab');
    console.log('   □ Test with screen reader');
    console.log('   □ Check responsive design on mobile');
    console.log('   □ Test with different browsers');
    console.log('   □ Verify security (XSS protection)');
  }
}

// Auto-execute if on the correct page
if (window.location.pathname.includes('/dashboard/tags')) {
  const testSuite = new TagsTestSuite();
  
  // Wait for page to load fully
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(() => testSuite.runAllTests(), 1000);
    });
  } else {
    setTimeout(() => testSuite.runAllTests(), 1000);
  }
  
  // Make available globally for manual testing
  window.tagTestSuite = testSuite;
} else {
  console.log('📍 Please navigate to /dashboard/tags to run the test suite');
  console.log('💡 Or call window.tagTestSuite.runAllTests() manually');
}
