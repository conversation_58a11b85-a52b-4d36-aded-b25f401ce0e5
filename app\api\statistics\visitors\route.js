import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url);
    const timeRange = searchParams.get('timeRange') || 'month';
    
    // Tentukan rentang waktu
    const now = new Date();
    let startDate = new Date();
    
    if (timeRange === 'week') {
      startDate.setDate(now.getDate() - 7);
    } else if (timeRange === 'month') {
      startDate.setMonth(now.getMonth() - 1);
    } else if (timeRange === 'year') {
      startDate.setFullYear(now.getFullYear() - 1);
    }
    
    // Hitung total pengunjung unik berdasarkan IP
    const uniqueVisitors = await prisma.pageVisit.groupBy({
      by: ['ipAddress'],
      where: {
        timestamp: {
          gte: startDate
        }
      },
      _count: true
    });
    
    // Hitung kunjungan per hari
    const dailyVisits = await prisma.$queryRaw`
      SELECT DATE(timestamp) as date, COUNT(DISTINCT ipAddress) as count
      FROM PageVisit
      WHERE timestamp >= ${startDate}
      GROUP BY DATE(timestamp)
      ORDER BY date ASC
    `;
    
    // Halaman yang paling sering dikunjungi
    const topPages = await prisma.pageVisit.groupBy({
      by: ['url'],
      where: {
        timestamp: {
          gte: startDate
        }
      },
      _count: {
        id: true
      },
      orderBy: {
        _count: {
          id: 'desc'
        }
      },
      take: 5
    });
    
    return NextResponse.json({
      uniqueVisitors: uniqueVisitors.length,
      dailyVisits,
      topPages: topPages.map(page => ({
        url: page.url,
        visits: page._count.id
      }))
    });
  } catch (error) {
    console.error('Error fetching visitor statistics:', error);
    return NextResponse.json(
      { error: 'Gagal mengambil statistik pengunjung' },
      { status: 500 }
    );
  }
}