# Formulir Permohonan Informasi PPID BPMP

Formulir permohonan informasi PPID BPMP Provinsi Kalimantan Timur telah berhasil dibuat dengan fitur-fitur berikut:

## Fitur Yang Telah Diimplementasi

### 1. <PERSON><PERSON> Formulir Permohonan (`/permohonan`)
- **Lokasi**: `f:\online\ppid\app\permohonan\page.js`
- **URL**: `http://localhost:3000/permohonan`

**Field yang tersedia:**
- Tanggal permohonan informasi (otomatis hari ini)
- <PERSON><PERSON><PERSON> pemohon (radio button):
  - Perorangan
  - Organisasi/lembaga masyarakat atau yayasan atau badan usaha
  - Instansi pemerintah
- Nomor Induk <PERSON>dukan (NIK)
- Nama sesuai KTP
- Alamat lengkap sesuai KTP
- Alamat tinggal saat ini
- Nomor kontak (HP/WA)
- Alamat surat elektronik (email)
- Pekerjaan
- Informasi yang diminta beserta rinciannya
- Tujuan permohonan informasi
- Bentuk informasi yang diminta (radio button):
  - Tercetak (hardcopy)
  - Digital (softcopy)
- Cara mendapatkan informasi (radio button):
  - Melihat atau mengetahui
  - Meminta salinan bentuk tercetak (hardcopy)
  - Meminta salinan bentuk digital (softcopy)

**Upload File:**
- Formulir permohonan informasi (PDF, max 1MB)
- Surat pernyataan bermaterai (PDF, max 1MB)
- Scan/Foto KTP (Image, max 1MB)

### 2. Database Schema
- **Lokasi**: `f:\online\ppid\prisma\schema.prisma`
- **Model**: `PermohonanInformasi`
- **Fields**: Menyimpan semua data formulir, file paths, dan status tracking

### 3. API Endpoints

#### POST `/api/permohonan`
- **Lokasi**: `f:\online\ppid\app\api\permohonan\route.ts`
- **Fungsi**: Menerima dan menyimpan data permohonan
- **Fitur**:
  - Validasi file upload (ukuran dan tipe)
  - Penyimpanan file ke `public/uploads/permohonan/`
  - Penyimpanan data ke database

#### GET `/api/permohonan`
- **Fungsi**: Mengambil daftar permohonan dengan pagination dan filter
- **Parameters**: page, limit, status

#### GET/PATCH/DELETE `/api/permohonan/[id]`
- **Lokasi**: `f:\online\ppid\app\api\permohonan\[id]\route.ts`
- **Fungsi**: CRUD operations untuk permohonan specific

### 4. Halaman Admin

#### Daftar Permohonan (`/admin/permohonan`)
- **Lokasi**: `f:\online\ppid\app\admin\permohonan\page.js`
- **Fitur**:
  - Tabel daftar permohonan
  - Filter berdasarkan status
  - Search berdasarkan nama, NIK, email
  - Pagination
  - Update status permohonan

#### Detail Permohonan (`/admin/permohonan/[id]`)
- **Lokasi**: `f:\online\ppid\app\admin\permohonan\[id]\page.js`
- **Fitur**:
  - Detail lengkap permohonan
  - Download file yang diupload
  - Update status (pending → diproses → selesai/ditolak)
  - Catatan admin internal
  - Tanggapan untuk pemohon

### 5. Komponen UI
- **Lokasi**: `f:\online\ppid\app\components\ui/`
- **Komponen yang dibuat**:
  - `card.js` - Card components
  - `button.js` - Button component dengan variants
  - `input.js` - Input component
  - `label.js` - Label component
  - `textarea.js` - Textarea component
  - `radio-group.js` - Radio group component
  - `alert.js` - Alert component
  - `badge.js` - Badge component

## Status Tracking

Permohonan memiliki 4 status:
1. **pending** - Permohonan baru masuk
2. **diproses** - Sedang diproses admin
3. **selesai** - Permohonan telah diselesaikan
4. **ditolak** - Permohonan ditolak

## File Upload

File yang diupload disimpan di:
- **Path**: `public/uploads/permohonan/`
- **Naming**: UUID + file extension
- **Validasi**: 
  - PDF files untuk formulir dan surat pernyataan
  - Image files untuk scan KTP
  - Maximum 1MB per file

## Dependencies Yang Ditambahkan

```json
{
  "@radix-ui/react-radio-group": "^latest",
  "@radix-ui/react-slot": "^latest",
  "class-variance-authority": "^latest",
  "uuid": "^latest",
  "@types/uuid": "^latest",
  "clsx": "^latest",
  "tailwind-merge": "^latest"
}
```

## Cara Menggunakan

1. **Akses Formulir**: Buka `http://localhost:3000/permohonan`
2. **Isi Formulir**: Lengkapi semua field yang required (bertanda *)
3. **Upload File**: Upload 3 file yang diperlukan
4. **Submit**: Klik tombol "Kirim"
5. **Admin Review**: Admin dapat melihat di `/admin/permohonan`

## Keamanan

- File upload dengan validasi tipe dan ukuran
- Data disimpan dengan UUID untuk keamanan
- Form validation di client dan server side
- Sanitasi input data

## Database Migration

Untuk menerapkan schema ke database:
```bash
npx prisma db push
npx prisma generate
```

Formulir permohonan informasi PPID BPMP telah siap digunakan dan dapat diakses di `http://localhost:3000/permohonan`.
