// Test Prisma Client
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient();

async function testPrismaClient() {
  try {
    console.log('Testing Prisma Client...');
    
    // Test basic connection
    await prisma.$connect();
    console.log('✅ Database connection successful');
    
    // Check available models
    console.log('Available Prisma models:');
    console.log(Object.keys(prisma).filter(key => !key.startsWith('$') && !key.startsWith('_')));
    
    // Test if refreshToken model exists
    if (prisma.refreshToken) {
      console.log('✅ refreshToken model found (camelCase)');
    } else if (prisma.refreshtoken) {
      console.log('✅ refreshtoken model found (lowercase)');
    } else {
      console.log('❌ No refresh token model found');
    }
    
    // Try to count refresh tokens
    try {
      const count = await (prisma.refreshToken || prisma.refreshtoken).count();
      console.log(`Refresh tokens in database: ${count}`);
    } catch (error) {
      console.log('Error accessing refresh token model:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error:', error.message);
  } finally {
    await prisma.$disconnect();
  }
}

testPrismaClient();
