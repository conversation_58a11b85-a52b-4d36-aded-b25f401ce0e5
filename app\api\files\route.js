import { NextResponse } from 'next/server';
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function GET(request) {
  try {
    // Ambil query parameters jika ada
    const { searchParams } = new URL(request.url);
    const category = searchParams.get('category');
    const isPublic = searchParams.get('isPublic');
    
    // Buat filter berdasarkan query parameters
    const filter = {};
    if (category) filter.category = category;
    if (isPublic !== null) filter.isPublic = isPublic === 'true';
    
    // Ambil data file dari database
    const files = await prisma.file.findMany({
      where: filter,
      orderBy: {
        uploadedAt: 'desc'
      }
    });
    
    return NextResponse.json({
      success: true,
      files
    });
    
  } catch (error) {
    console.error('Error fetching files:', error);
    return NextResponse.json(
      { success: false, error: '<PERSON><PERSON><PERSON>di kesalahan saat mengambil data file' },
      { status: 500 }
    );
  }
}