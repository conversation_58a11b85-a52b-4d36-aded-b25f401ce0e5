# 🚀 Enhanced Tags Database Migration Guide

## 📋 Daftar Isi
1. [<PERSON><PERSON>](#persiapan-migrasi)
2. [Backup Data](#backup-data)
3. [<PERSON><PERSON><PERSON><PERSON>grasi](#menjalankan-migrasi)
4. [<PERSON><PERSON><PERSON><PERSON><PERSON>](#verifikasi-migrasi)
5. [Rollback (<PERSON><PERSON>)](#rollback)
6. [Fitur Baru <PERSON>](#fitur-baru)

## 🔧 Persiapan Migrasi

### Prerequisites
- [x] Node.js terinstall
- [x] Database MySQL/MariaDB running
- [x] Prisma CLI tersedia
- [x] Tidak ada user yang aktif menggunakan sistem
- [x] Backup database manual sudah dibuat

### Struktur File Migrasi
```
f:\online\ppid\
├── migrate-tags.bat              # Script utama untuk Windows
├── prisma\
│   ├── migrate-enhanced-tags.js  # Script Node.js untuk migrasi
│   ├── schema.prisma             # Schema yang sudah diupdate
│   ├── migrations\
│   │   └── manual-enhanced-tags.sql  # SQL manual jika diperlukan
│   └── backup\                   # Folder backup (akan dibuat otomatis)
```

## 💾 Backup Data

### Backup Otomatis
Script akan otomatis membuat backup dalam format JSON:
```bash
# Backup otomatis saat menjalankan migrasi
cd f:\online\ppid
migrate-tags.bat backup
```

### Backup Manual Database (SANGAT DIREKOMENDASIKAN)
```sql
-- Buat backup lengkap database
mysqldump -u username -p database_name > ppid_backup_before_tags_migration.sql

-- Atau backup tabel specific
mysqldump -u username -p database_name tags tags_on_posts > tags_backup.sql
```

## 🚀 Menjalankan Migrasi

### Metode 1: Menggunakan Script Batch (RECOMMENDED)
```cmd
# Buka Command Prompt sebagai Administrator
cd f:\online\ppid

# Jalankan script migrasi
migrate-tags.bat

# Pilih opsi dari menu:
# 1. Backup current data
# 2. Validate current schema  
# 3. Run full migration
```

### Metode 2: Manual Step-by-Step
```cmd
# 1. Backup data
node prisma\migrate-enhanced-tags.js backup

# 2. Validasi schema saat ini
node prisma\migrate-enhanced-tags.js validate

# 3. Generate Prisma client
npx prisma generate

# 4. Jalankan migrasi Prisma
npx prisma migrate dev --name enhanced-tags-features

# 5. Update data existing
node prisma\migrate-enhanced-tags.js migrate
```

### Metode 3: Menggunakan Prisma Studio (Untuk Monitoring)
```cmd
# Buka Prisma Studio untuk monitoring
npx prisma studio
```

## ✅ Verifikasi Migrasi

### 1. Cek Schema Database
```sql
-- Cek kolom baru di tabel tags
DESCRIBE tags;

-- Harus ada kolom baru:
-- isActive, priority, color, icon, createdBy, updatedBy

-- Cek kolom baru di tabel tags_on_posts  
DESCRIBE tags_on_posts;

-- Harus ada kolom baru:
-- addedAt, addedBy
```

### 2. Cek Data
```sql
-- Cek apakah data existing masih ada
SELECT COUNT(*) FROM tags;

-- Cek apakah kolom baru terisi default values
SELECT id, name, isActive, priority, createdAt FROM tags LIMIT 5;

-- Cek relasi tags_on_posts
SELECT COUNT(*) FROM tags_on_posts;
```

### 3. Test API Endpoints
```bash
# Test GET tags
curl http://localhost:3000/api/tags

# Response harus include field baru:
# isActive, priority, color, icon, createdBy, updatedBy
```

### 4. Test Frontend
- [ ] Buka halaman `/dashboard/tags`
- [ ] Pastikan tabel masih menampilkan data
- [ ] Test create tag baru
- [ ] Test edit tag existing
- [ ] Test delete tag

## 🔄 Rollback (Jika Diperlukan)

### Rollback Otomatis (Limited)
```cmd
cd f:\online\ppid
node prisma\migrate-enhanced-tags.js rollback
```

### Rollback Manual
```sql
-- 1. Restore dari backup database
mysql -u username -p database_name < ppid_backup_before_tags_migration.sql

-- 2. Atau restore schema lama dan data dari JSON backup
-- (Gunakan file backup JSON yang dibuat otomatis)
```

### Revert Prisma Schema
```cmd
# 1. Restore schema.prisma ke versi sebelumnya dari git
git checkout HEAD~1 -- prisma/schema.prisma

# 2. Generate ulang
npx prisma generate

# 3. Reset migrasi
npx prisma migrate reset --force
```

## 🆕 Fitur Baru Setelah Migrasi

### Schema Enhancements

#### 1. **Soft Delete**
```javascript
// Soft delete tag instead of hard delete
await prisma.tag.update({
  where: { id: tagId },
  data: { isActive: false }
});

// Query only active tags
const activeTags = await prisma.tag.findMany({
  where: { isActive: true }
});
```

#### 2. **Tag Prioritization**
```javascript
// Get tags ordered by priority
const prioritizedTags = await prisma.tag.findMany({
  where: { isActive: true },
  orderBy: [
    { priority: 'desc' },
    { name: 'asc' }
  ]
});
```

#### 3. **Audit Trail**
```javascript
// Create tag with audit info
const newTag = await prisma.tag.create({
  data: {
    name: 'New Tag',
    slug: 'new-tag',
    createdBy: userId,
    priority: 10
  }
});

// Update tag with audit info
await prisma.tag.update({
  where: { id: tagId },
  data: {
    name: 'Updated Name',
    updatedBy: userId
  }
});
```

#### 4. **UI Enhancements**
```javascript
// Tag dengan color dan icon
const visualTag = await prisma.tag.create({
  data: {
    name: 'Important',
    slug: 'important',
    color: '#FF0000',
    icon: 'exclamation-triangle',
    priority: 100
  }
});
```

#### 5. **Enhanced Relationships**
```javascript
// Track siapa yang menambahkan tag ke post
await prisma.tagsOnPosts.create({
  data: {
    postId: postId,
    tagId: tagId,
    addedBy: userId,
    addedAt: new Date()
  }
});

// Query dengan relasi lengkap
const tagsWithAudit = await prisma.tag.findMany({
  include: {
    creator: { select: { username: true } },
    updater: { select: { username: true } },
    _count: { select: { posts: true } }
  }
});
```

### Performance Improvements

#### 1. **New Indexes**
- `idx_tags_name` - Pencarian berdasarkan nama
- `idx_tags_isActive` - Filter active tags
- `idx_tags_priority_name` - Sorting berdasarkan priority
- `idx_tags_createdAt` - Query chronological
- `idx_tags_createdBy` - Filter berdasarkan creator

#### 2. **Query Optimization**
```javascript
// Efficient tag search dengan index
const searchTags = await prisma.tag.findMany({
  where: {
    isActive: true,
    name: {
      contains: searchTerm,
      mode: 'insensitive'
    }
  },
  orderBy: [
    { priority: 'desc' },
    { name: 'asc' }
  ]
});
```

## 🚨 Troubleshooting

### Error: Migration Failed
```cmd
# 1. Check Prisma logs
npx prisma migrate status

# 2. Check database connection
npx prisma db push --preview-feature

# 3. Manual SQL execution
# Gunakan file manual-enhanced-tags.sql
```

### Error: Constraint Violations
```sql
-- Check for invalid data
SELECT * FROM tags WHERE name IS NULL OR name = '';
SELECT * FROM tags WHERE slug IS NULL OR slug = '';

-- Fix invalid data before migration
UPDATE tags SET name = CONCAT('Tag_', id) WHERE name IS NULL OR name = '';
UPDATE tags SET slug = CONCAT('tag_', id) WHERE slug IS NULL OR slug = '';
```

### Error: Permission Denied
```cmd
# Run as Administrator
# Ensure database user has ALTER privileges
GRANT ALTER ON database_name.* TO 'username'@'localhost';
```

## 📞 Support

Jika ada masalah selama migrasi:

1. **Stop aplikasi** segera
2. **Check backup files** di folder `prisma/backup/`
3. **Review error logs** di console
4. **Contact administrator** dengan detail error
5. **Gunakan rollback** jika diperlukan

---

**⚠️ PENTING:** 
- Selalu test di development environment dulu
- Backup database sebelum migrasi production
- Lakukan migrasi di luar jam operasional
- Monitor aplikasi setelah migrasi selesai
