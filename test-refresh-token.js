const { PrismaClient } = require('@prisma/client');

async function testRefreshToken() {
  const prisma = new PrismaClient();
  
  try {
    console.log('Testing refreshtoken model...');
    
    // Test with lowercase
    const count = await prisma.refreshtoken.count();
    console.log('✅ refreshtoken (lowercase) works! Count:', count);
    
  } catch (error) {
    console.log('❌ Error with refreshtoken (lowercase):', error.message);
    
    try {
      // Test with camelCase
      const count2 = await prisma.refreshToken.count();
      console.log('✅ refreshToken (camelCase) works! Count:', count2);
    } catch (error2) {
      console.log('❌ Error with refreshToken (camelCase):', error2.message);
    }
  } finally {
    await prisma.$disconnect();
  }
}

testRefreshToken();
