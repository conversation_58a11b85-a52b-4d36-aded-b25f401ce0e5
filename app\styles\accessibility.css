/* Large Text Mode */
.large-text {
  font-size: 120%;
  line-height: 1.5;
}

.large-text h1, .large-text h2, .large-text h3, 
.large-text h4, .large-text h5, .large-text h6 {
  font-size: 130%;
  line-height: 1.3;
}

.large-text button, .large-text input, .large-text select, .large-text textarea {
  font-size: 120%;
}

.large-text a {
  padding: 0.2em 0;
}

/* High Contrast Mode */
.high-contrast {
  background-color: #000 !important;
  color: #fff !important;
}

.high-contrast a:not(.btn) {
  color: #ffff00 !important;
  text-decoration: underline !important;
}

.high-contrast button, 
.high-contrast .btn,
.high-contrast input[type="button"],
.high-contrast input[type="submit"] {
  background-color: #ffff00 !important;
  color: #000 !important;
  border: 2px solid #ffff00 !important;
  font-weight: bold !important;
}

.high-contrast img {
  filter: grayscale(100%) contrast(150%);
}

.high-contrast .bg-gradient-to-r,
.high-contrast .bg-gradient-to-br,
.high-contrast [class^="bg-"] {
  background: #222 !important;
  background-image: none !important;
}

.high-contrast input, 
.high-contrast textarea, 
.high-contrast select {
  background: #000 !important;
  color: #fff !important;
  border: 1px solid #fff !important;
}

.high-contrast ::placeholder {
  color: #999 !important;
}

.high-contrast h1, .high-contrast h2, .high-contrast h3,
.high-contrast h4, .high-contrast h5, .high-contrast h6 {
  color: #ffff00 !important;
}

/* Reduced Motion */
.reduced-motion * {
  animation: none !important;
  transition: none !important;
  transform: none !important;
}

.reduced-motion [class*="motion-"],
.reduced-motion [class*="animate-"] {
  animation: none !important;
  transition: none !important;
}

.reduced-motion .hover\: {
  transition: none !important;
}

/* Dyslexic Font */
@font-face {
  font-family: 'OpenDyslexic';
  src: url('https://cdn.jsdelivr.net/npm/open-dyslexic@1.0.3/woff/OpenDyslexic-Regular.woff') format('woff');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

@font-face {
  font-family: 'OpenDyslexic';
  src: url('https://cdn.jsdelivr.net/npm/open-dyslexic@1.0.3/woff/OpenDyslexic-Bold.woff') format('woff');
  font-weight: bold;
  font-style: normal;
  font-display: swap;
}

.dyslexic-font * {
  font-family: 'OpenDyslexic', 'Comic Sans MS', sans-serif !important;
  letter-spacing: 0.05em !important;
  word-spacing: 0.15em !important;
  line-height: 1.5 !important;
}

.dyslexic-font p {
  max-width: 70ch !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

/* Focus Mode */
.focus-mode main {
  max-width: 800px !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.focus-mode p, .focus-mode h1, .focus-mode h2, .focus-mode h3, 
.focus-mode h4, .focus-mode h5, .focus-mode h6, .focus-mode li {
  max-width: 65ch !important;
  margin-left: auto !important;
  margin-right: auto !important;
}

.focus-mode aside, 
.focus-mode .sidebar, 
.focus-mode [role="complementary"] {
  opacity: 0.5 !important;
}

.focus-mode main:hover aside,
.focus-mode main:hover .sidebar,
.focus-mode main:hover [role="complementary"] {
  opacity: 1 !important;
}

/* Active speech element highlighting */
.tts-active {
  background-color: rgba(255, 190, 152, 0.3) !important;
  border-radius: 4px !important;
  box-shadow: 0 0 0 2px rgba(217, 194, 255, 0.5) !important;
  position: relative !important;
  animation: pulse-highlight 2s infinite !important;
}

@keyframes pulse-highlight {
  0% { box-shadow: 0 0 0 2px rgba(217, 194, 255, 0.5) !important; }
  50% { box-shadow: 0 0 0 4px rgba(217, 194, 255, 0.3) !important; }
  100% { box-shadow: 0 0 0 2px rgba(217, 194, 255, 0.5) !important; }
}

/* Make elements readable by text-to-speech on hover */
[data-tts-enabled]:not(button):not(a):hover {
  cursor: context-menu !important;
  background-color: rgba(255, 190, 152, 0.1) !important;
  border-radius: 2px !important;
  position: relative !important;
}

[data-tts-enabled]:not(button):not(a):hover::after {
  content: "🔊" !important;
  position: absolute !important;
  right: -20px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  font-size: 14px !important;
  opacity: 0.7 !important;
}

/* For buttons and links, we use a different approach */
button[data-tts-enabled]:hover,
a[data-tts-enabled]:hover {
  outline: 2px dashed rgba(255, 190, 152, 0.5) !important;
  outline-offset: 2px !important;
}

/* Accessibility focused outline */
:focus-visible {
  outline: 3px solid #FFBE98 !important;
  outline-offset: 2px !important;
}

/* Skip to content focus styles */
a[href="#main-content"]:focus {
  transform: translateY(0) !important;
}

/* Improved link and button focus for accessibility */
a:focus, button:focus {
  outline: 3px solid #FFBE98 !important;
  outline-offset: 2px !important;
}

/* Add main content anchor */
[id="main-content"] {
  scroll-margin-top: 80px;
}

/* Screen reader only class */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Status announcement for TTS */
.tts-status {
  position: fixed;
  bottom: 70px;
  right: 70px;
  padding: 8px 12px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border-radius: 4px;
  font-size: 14px;
  pointer-events: none;
  z-index: 1000;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tts-status.visible {
  opacity: 1;
}