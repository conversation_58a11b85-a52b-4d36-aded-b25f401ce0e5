'use client';

import { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'react-hot-toast';

const AuthContext = createContext(undefined);

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);  const router = useRouter();  

  // Check authentication status - fungsi utama  
  const checkAuthStatus = useCallback(async () => {
    try {
      // Jangan set loading true jika sudah ada user
      if (!user) {
        setLoading(true);
      }
      const response = await fetch('/api/auth/me', {
        credentials: 'include', // Include cookies in the request
      });
      if (response.ok) {
        const userData = await response.json();
        // Only set user if valid and has id
        if (userData && userData.user && userData.user.id) {
          setUser(userData.user);
        } else {
          setUser(null);
        }
        return true;
      } else {
        // Token tidak valid
        setUser(null);
        return false;
      }
    } catch (error) {
      console.error('Auth check failed:', error);
      setUser(null);
      return false;
    } finally {
      setLoading(false);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Menghapus dependency user untuk mencegah infinite loop

  // Alias untuk kompatibilitas
  const checkAuth = checkAuthStatus;  // Check auth on mount
  useEffect(() => {
    checkAuthStatus();
  }, [checkAuthStatus]);

  const login = async (username, password) => {
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ username, password }),
        credentials: 'include', // Include cookies in the request
      });

      const data = await response.json();

      if (response.ok) {
        // No need to manually store tokens since they're set as httpOnly cookies
        setUser(data.user);
        toast.success('Login berhasil!');
        return { success: true };
      } else {
        toast.error(data.error || 'Login gagal');
        return { success: false, error: data.error };
      }
    } catch (error) {
      console.error('Login error:', error);
      toast.error('Terjadi kesalahan saat login');
      return { success: false, error: 'Network error' };
    }
  };
  const logout = async () => {
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include', // Include cookies in the request
      });
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setUser(null);
      router.push('/login');
      toast.success('Logout berhasil');
    }
  };
  const value = {
    user,
    loading,
    login,
    logout,
    checkAuth,
    checkAuthStatus, // Tambahkan fungsi ini
    initialized: !loading, // Property yang diperlukan DashboardLayout
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}





