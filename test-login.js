// Test Login API
const { createRefreshToken } = require('./app/lib/auth.js');

async function testLogin() {
  try {
    console.log('Testing createRefreshToken function...');
    
    // Test creating a refresh token
    const testUserId = 'test-user-id';
    const token = await createRefreshToken(testUserId);
    
    console.log('✅ Refresh token created successfully!');
    console.log('Token length:', token.length);
    
  } catch (error) {
    console.log('❌ Error creating refresh token:', error.message);
    console.log('Stack:', error.stack);
  }
}

testLogin();
