@echo off
REM Enhanced Tags Dashboard - Final Deployment Script
REM Run this script to validate and deploy enhanced tags system

echo.
echo ================================================
echo   Enhanced Tags Dashboard - Final Deployment
echo ================================================
echo.

echo [1/6] Checking file structure...
if exist "app\components\EnhancedTagsTable.jsx" (
    echo ✓ EnhancedTagsTable.jsx found
) else (
    echo ✗ EnhancedTagsTable.jsx missing
    goto :error
)

if exist "app\components\TagFormModal.jsx" (
    echo ✓ TagFormModal.jsx found
) else (
    echo ✗ TagFormModal.jsx missing
    goto :error
)

if exist "app\lib\validation.js" (
    echo ✓ validation.js found
) else (
    echo ✗ validation.js missing
    goto :error
)

if exist "app\dashboard\tags\page.js" (
    echo ✓ Enhanced page.js found
) else (
    echo ✗ Enhanced page.js missing
    goto :error
)

echo.
echo [2/6] Validating database schema...
npx prisma validate
if errorlevel 1 (
    echo ✗ Database schema validation failed
    goto :error
) else (
    echo ✓ Database schema is valid
)

echo.
echo [3/6] Checking dependencies...
npm list react-hot-toast >nul 2>&1
if errorlevel 1 (
    echo Installing react-hot-toast...
    npm install react-hot-toast
)

npm list @heroicons/react >nul 2>&1
if errorlevel 1 (
    echo Installing heroicons...
    npm install @heroicons/react
)

npm list zod >nul 2>&1
if errorlevel 1 (
    echo Installing zod for validation...
    npm install zod
)

npm list dompurify >nul 2>&1
if errorlevel 1 (
    echo Installing dompurify for security...
    npm install dompurify
    npm install --save-dev @types/dompurify
)

echo ✓ All dependencies checked

echo.
echo [4/6] Running build test...
npm run build >build.log 2>&1
if errorlevel 1 (
    echo ✗ Build failed - check build.log for details
    type build.log
    goto :error
) else (
    echo ✓ Build successful
    del build.log >nul 2>&1
)

echo.
echo [5/6] Database migration check...
npx prisma db push --preview-feature >migration.log 2>&1
if errorlevel 1 (
    echo ⚠ Database migration had issues - check migration.log
    type migration.log
) else (
    echo ✓ Database schema synchronized
    del migration.log >nul 2>&1
)

echo.
echo [6/6] Starting enhanced dashboard...
echo.
echo ================================================
echo           DEPLOYMENT SUCCESSFUL! ✓
echo ================================================
echo.
echo Enhanced Tags Dashboard is ready!
echo.
echo 🚀 Quick Start:
echo   • Navigate to: http://localhost:3000/dashboard/tags
echo   • Press Ctrl+N to create new tag
echo   • Use search box for filtering
echo   • Click column headers to sort
echo.
echo 🧪 Testing:
echo   • Open browser console at /dashboard/tags
echo   • Auto-test suite will run automatically
echo   • Or run: window.tagTestSuite.runAllTests()
echo.
echo 📚 Documentation:
echo   • User Guide: ENHANCED-TAGS-GUIDE.md
echo   • Implementation: IMPLEMENTATION-COMPLETE.md
echo   • Migration Guide: MIGRATION-GUIDE.md
echo.
echo Starting development server...
npm run dev
goto :end

:error
echo.
echo ================================================
echo              DEPLOYMENT FAILED! ✗
echo ================================================
echo.
echo Please check the errors above and retry.
echo.
echo Common solutions:
echo   • Run: npm install
echo   • Check file paths and permissions
echo   • Verify database connection
echo   • Review error logs for details
echo.
pause
exit /b 1

:end
