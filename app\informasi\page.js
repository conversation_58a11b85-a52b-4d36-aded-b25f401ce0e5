// LayananInformasi.js
"use client"
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { cards } from './../components/cardData';
import Nav from './../components/Nav'
import Modal from './../components/Modal';
import { fadeIn, staggerContainer, slideInUp, shouldReduceMotion } from './../components/AnimationConfig';
import {
  MagnifyingGlassIcon,
  QrCodeIcon,
  DocumentTextIcon,
  CalendarIcon,
  UserIcon,
  IdentificationIcon,
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  XCircleIcon
} from '@heroicons/react/24/outline';
import Link from 'next/link';

const LayananInformasi = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalContent, setModalContent] = useState({ title: '', content: '' });
  const [hoveredCard, setHoveredCard] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('Semua');
  const [loading, setLoading] = useState(true);
  const prefersReducedMotion = shouldReduceMotion();

  // Tracking states
  const [trackingId, setTrackingId] = useState('');
  const [isTracking, setIsTracking] = useState(false);
  const [trackingResult, setTrackingResult] = useState(null);
  const [trackingError, setTrackingError] = useState(null);
  const [showTracking, setShowTracking] = useState(false);
  
  useEffect(() => {
    // Simulasi loading
    const timer = setTimeout(() => {
      setLoading(false);
    }, 800);

    // Check for tracking parameter in URL
    const urlParams = new URLSearchParams(window.location.search);
    const trackParam = urlParams.get('track');
    if (trackParam && /^\d{6}$/.test(trackParam)) {
      setTrackingId(trackParam);
      // Auto-trigger tracking
      setTimeout(() => {
        handleTrackingFromURL(trackParam);
      }, 1000);
    }

    return () => clearTimeout(timer);
  }, []);

  // Handle tracking from URL parameter
  const handleTrackingFromURL = async (id) => {
    setIsTracking(true);
    setTrackingError(null);
    setTrackingResult(null);

    try {
      const response = await fetch(`/api/permohonan/${id}`);

      if (response.ok) {
        const data = await response.json();
        setTrackingResult(data);
        setShowTracking(true);
      } else if (response.status === 404) {
        setTrackingError('ID permohonan tidak ditemukan.');
      } else {
        setTrackingError('Terjadi kesalahan saat mencari permohonan.');
      }
    } catch (error) {
      console.error('Error tracking:', error);
      setTrackingError('Terjadi kesalahan koneksi.');
    } finally {
      setIsTracking(false);
    }
  };
  
  // Dapatkan semua kategori unik
  const categories = ['Semua', ...new Set(cards.map(card => card.category))];
  
  // Filter kartu berdasarkan pencarian dan kategori
  const filteredCards = cards.filter(card => 
    (activeCategory === 'Semua' || card.category === activeCategory) &&
    (card.title.toLowerCase().includes(searchTerm.toLowerCase()) || 
     card.description.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const openModal = (card) => {
    if (card.modalContent) {
      setModalContent({
        title: card.modalContent.title,
        content: card.modalContent.content
      });
      setIsModalOpen(true);
    } else {
      // Modal content not found
    }
  };

  const closeModal = () => {
    setIsModalOpen(false);
  };

  // Tracking functions
  const handleTracking = async (e) => {
    e.preventDefault();
    if (!trackingId.trim()) {
      setTrackingError('Masukkan ID permohonan');
      return;
    }

    if (trackingId.length !== 6 || !/^\d{6}$/.test(trackingId)) {
      setTrackingError('ID permohonan harus 6 digit angka');
      return;
    }

    setIsTracking(true);
    setTrackingError(null);
    setTrackingResult(null);

    try {
      const response = await fetch(`/api/permohonan/${trackingId}`);

      if (response.ok) {
        const data = await response.json();
        setTrackingResult(data);
        setShowTracking(true);
      } else if (response.status === 404) {
        setTrackingError('ID permohonan tidak ditemukan. Pastikan ID yang Anda masukkan benar.');
      } else {
        setTrackingError('Terjadi kesalahan saat mencari permohonan. Silakan coba lagi.');
      }
    } catch (error) {
      console.error('Error tracking:', error);
      setTrackingError('Terjadi kesalahan koneksi. Silakan coba lagi.');
    } finally {
      setIsTracking(false);
    }
  };

  const resetTracking = () => {
    setTrackingId('');
    setTrackingResult(null);
    setTrackingError(null);
    setShowTracking(false);
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-6 w-6 text-yellow-500" />;
      case 'diproses':
        return <ExclamationTriangleIcon className="h-6 w-6 text-blue-500" />;
      case 'selesai':
        return <CheckCircleIcon className="h-6 w-6 text-green-500" />;
      case 'ditolak':
        return <XCircleIcon className="h-6 w-6 text-red-500" />;
      default:
        return <ClockIcon className="h-6 w-6 text-gray-500" />;
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'diproses':
        return 'bg-blue-100 text-blue-800';
      case 'selesai':
        return 'bg-green-100 text-green-800';
      case 'ditolak':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="min-h-screen bg-gradient-blue-purple">
      <Nav/>
      <motion.div 
        className="container px-4 py-8 mx-auto mt-20"
        {...(prefersReducedMotion ? {} : fadeIn)}
        transition={{ duration: 0.5 }}
      >
        <motion.h1
          className="mb-6 text-3xl font-bold text-center text-gray-800"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          Layanan Informasi & Tracking
        </motion.h1>

        {/* Tracking Section - Compact */}
        <motion.div
          className="max-w-2xl mx-auto mb-8 p-4 bg-white rounded-lg shadow-md"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <div className="text-center mb-4">
            <div className="flex items-center justify-center w-12 h-12 mx-auto mb-3 text-white rounded-full bg-blue-500">
              <QrCodeIcon className="w-6 h-6" />
            </div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">
              Tracking Permohonan
            </h2>
            <p className="text-sm text-gray-600">
              Masukkan ID 6 digit untuk melacak status permohonan
            </p>
          </div>

          {!showTracking ? (
            // Tracking Form - Compact
            <form onSubmit={handleTracking} className="max-w-sm mx-auto">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={trackingId}
                  onChange={(e) => setTrackingId(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  placeholder="ID 6 digit"
                  className="flex-1 px-3 py-2 text-center font-mono border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  maxLength="6"
                  pattern="[0-9]{6}"
                  disabled={isTracking}
                />
                <button
                  type="submit"
                  disabled={isTracking || trackingId.length !== 6}
                  className="px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-1"
                >
                  {isTracking ? (
                    <>
                      <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span className="text-sm">Cari</span>
                    </>
                  ) : (
                    <>
                      <MagnifyingGlassIcon className="w-4 h-4" />
                      <span className="text-sm">Lacak</span>
                    </>
                  )}
                </button>
              </div>

              {trackingError && (
                <div className="mt-3 p-2 bg-red-100 border border-red-400 text-red-700 rounded-md text-sm">
                  {trackingError}
                </div>
              )}

              <div className="mt-4 text-xs text-gray-500 text-center">
                <p>ID permohonan ada di bukti pengiriman.
                  <Link href="/permohonan" className="ml-1 text-blue-600 hover:text-blue-800 font-medium">
                    Buat permohonan →
                  </Link>
                </p>
              </div>
            </form>
          ) : (
            // Tracking Result - Compact
            <div className="max-w-3xl mx-auto">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-bold text-gray-900">Hasil Tracking</h3>
                <button
                  onClick={resetTracking}
                  className="px-3 py-1 text-sm text-blue-600 bg-blue-100 rounded-md hover:bg-blue-200 transition-colors"
                >
                  Lacak ID Lain
                </button>
              </div>

              {trackingResult && (
                <div className="space-y-4">
                  {/* Status Header - Compact */}
                  <div className="bg-gray-50 rounded-md p-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-semibold text-gray-900">Status Permohonan</h4>
                        <p className="text-sm text-gray-600">ID: {trackingResult.id}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        {getStatusIcon(trackingResult.status)}
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(trackingResult.status)}`}>
                          {trackingResult.status?.charAt(0).toUpperCase() + trackingResult.status?.slice(1)}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Detail Information - Compact */}
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                    {/* Data Pemohon */}
                    <div className="bg-white border rounded-md p-3">
                      <h5 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <UserIcon className="h-4 w-4 text-gray-600" />
                        Data Pemohon
                      </h5>
                      <div className="space-y-2">
                        <div>
                          <label className="text-xs font-medium text-gray-600">Nama Lengkap</label>
                          <p className="text-sm text-gray-900 font-medium">{trackingResult.namaSesuaiKtp}</p>
                        </div>
                        <div>
                          <label className="text-xs font-medium text-gray-600">Email</label>
                          <p className="text-sm text-gray-900">{trackingResult.alamatEmail}</p>
                        </div>
                        <div>
                          <label className="text-xs font-medium text-gray-600">No. Kontak</label>
                          <p className="text-sm text-gray-900">{trackingResult.nomorKontak}</p>
                        </div>
                        <div>
                          <label className="text-xs font-medium text-gray-600">Kategori Pemohon</label>
                          <p className="text-sm text-gray-900">{trackingResult.kategoriPemohon}</p>
                        </div>
                      </div>
                    </div>

                    {/* Detail Permohonan */}
                    <div className="bg-white border rounded-md p-3">
                      <h5 className="font-semibold text-gray-900 mb-3 flex items-center gap-2">
                        <DocumentTextIcon className="h-4 w-4 text-gray-600" />
                        Detail Permohonan
                      </h5>
                      <div className="space-y-2">
                        <div>
                          <label className="text-xs font-medium text-gray-600">Tanggal Permohonan</label>
                          <p className="text-sm text-gray-900 flex items-center gap-1">
                            <CalendarIcon className="h-3 w-3 text-gray-500" />
                            {formatDate(trackingResult.tanggalPermohonan)}
                          </p>
                        </div>
                        <div>
                          <label className="text-xs font-medium text-gray-600">Bentuk Informasi</label>
                          <p className="text-sm text-gray-900">{trackingResult.bentukInformasi}</p>
                        </div>
                        <div>
                          <label className="text-xs font-medium text-gray-600">Cara Mendapatkan</label>
                          <p className="text-sm text-gray-900">{trackingResult.caraMendapatkanInformasi}</p>
                        </div>
                        <div>
                          <label className="text-xs font-medium text-gray-600">Tanggal Dibuat</label>
                          <p className="text-sm text-gray-900">{formatDate(trackingResult.createdAt)}</p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Informasi yang Diminta - Compact */}
                  <div className="bg-white border rounded-md p-3">
                    <h5 className="font-semibold text-gray-900 mb-2">Informasi yang Diminta</h5>
                    <p className="text-sm text-gray-900 whitespace-pre-wrap">{trackingResult.informasiYangDiminta}</p>
                  </div>

                  {/* Tujuan Permohonan - Compact */}
                  <div className="bg-white border rounded-md p-3">
                    <h5 className="font-semibold text-gray-900 mb-2">Tujuan Permohonan</h5>
                    <p className="text-sm text-gray-900 whitespace-pre-wrap">{trackingResult.tujuanPermohonanInformasi}</p>
                  </div>

                  {/* Catatan Admin - Compact */}
                  {trackingResult.catatanAdmin && (
                    <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                      <h5 className="font-semibold text-blue-900 mb-2">Catatan Admin</h5>
                      <p className="text-sm text-blue-900 whitespace-pre-wrap">{trackingResult.catatanAdmin}</p>
                    </div>
                  )}

                  {/* Tanggapan Admin - Compact */}
                  {trackingResult.tanggapanAdmin && (
                    <div className="bg-green-50 border border-green-200 rounded-md p-3">
                      <h5 className="font-semibold text-green-900 mb-2">Tanggapan Admin</h5>
                      <p className="text-sm text-green-900 whitespace-pre-wrap">{trackingResult.tanggapanAdmin}</p>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}
        </motion.div>
        
        <motion.div 
          className="max-w-md mx-auto mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.3 }}
        >
          <div className="relative">
            <input
              type="text"
              placeholder="Cari layanan informasi..."
              className="w-full px-4 py-2 pl-10 border border-gray-300 rounded-full focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <svg className="absolute w-5 h-5 text-gray-400 left-3 top-3" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
            </svg>
          </div>
        </motion.div>
        
        <motion.div 
          className="flex flex-wrap justify-center gap-2 mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          {categories.map((category, index) => (
            <motion.button
              key={category}
              className={`px-4 py-2 text-sm font-medium rounded-full transition-colors ${
                activeCategory === category 
                  ? 'bg-primary-500 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              onClick={() => setActiveCategory(category)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {category}
            </motion.button>
          ))}
        </motion.div>
        
        <motion.div 
          className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3"
          variants={staggerContainer}
          initial="initial"
          animate="animate"
        >
          {loading ? (
            // Skeleton loading
            Array(6).fill().map((_, index) => (
              <div key={index} className="p-6 bg-white rounded-lg shadow-md animate-pulse">
                <div className="w-16 h-16 mb-6 bg-gray-200 rounded-full"></div>
                <div className="w-3/4 h-6 mb-3 bg-gray-200 rounded"></div>
                <div className="w-full h-4 mb-5 bg-gray-200 rounded"></div>
                <div className="w-1/3 h-10 bg-gray-200 rounded"></div>
              </div>
            ))
          ) : (
            // Kartu yang sudah difilter
            filteredCards.length > 0 ? (
              filteredCards.map((card, index) => (
                <motion.div 
                  key={index} 
                  className="relative p-6 overflow-hidden transition-all duration-300 bg-white rounded-lg shadow-md hover:shadow-lg"
                  variants={slideInUp}
                  transition={{ delay: index * 0.1 }}
                  whileHover={{ 
                    scale: 1.03, 
                    boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)" 
                  }}
                  onHoverStart={() => setHoveredCard(index)}
                  onHoverEnd={() => setHoveredCard(null)}
                >
                  <div className="absolute top-0 right-0 w-20 h-20 -mt-10 -mr-10 bg-blue-100 rounded-full opacity-50"></div>
                
                  <div className="relative z-10">
                    <motion.div 
                      className="flex items-center justify-center w-16 h-16 mb-6 text-white rounded-full bg-primary-500"
                      animate={{ 
                        scale: hoveredCard === index ? 1.1 : 1,
                        rotate: hoveredCard === index ? 5 : 0
                      }}
                      transition={{ type: "spring", stiffness: 300 }}
                    >
                      {card.icon}
                    </motion.div>
                  
                    <h2 className="mb-3 text-xl font-semibold text-gray-800">{card.title}</h2>
                    <p className="mb-5 text-gray-600">{card.description}</p>
                  
                    {card.action === 'link' ? (
                      <motion.a
                        href={card.link}
                        className="inline-flex items-center px-4 py-2 text-white transition duration-300 rounded bg-primary-500 hover:bg-primary-600"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Selengkapnya
                        <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                        </svg>
                      </motion.a>
                    ) : card.action === 'modal' ? (
                      <motion.button
                        onClick={() => openModal(card)}
                        className="inline-flex items-center px-4 py-2 text-white transition duration-300 rounded bg-primary-500 hover:bg-primary-600"
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        Selengkapnya
                        <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                      </motion.button>
                    ) : null}
                  </div>
                </motion.div>
              ))
            ) : (
              <motion.div 
                className="col-span-1 p-6 text-center md:col-span-2 lg:col-span-3"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
              >
                <p className="text-lg text-gray-600">Tidak ada layanan informasi yang sesuai dengan pencarian Anda.</p>
              </motion.div>
            )
          )}
        </motion.div>
        <Modal
          isOpen={isModalOpen}
          closeModal={closeModal}
          title={modalContent.title}
          content={modalContent.content}
        />
      </motion.div>
    </div>
  );
};

export default LayananInformasi;
