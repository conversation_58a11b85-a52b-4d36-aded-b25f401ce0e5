// cardData.js
import { 
    DocumentTextIcon, 
    ClipboardDocumentListIcon, 
    DocumentDuplicateIcon, 
    DocumentCheckIcon, 
    QuestionMarkCircleIcon, 
    ChatBubbleLeftRightIcon 
} from '@heroicons/react/24/outline';

export const cards = [
    {
        title: 'Maklumat Pelayanan',
        description: 'Lihat maklumat pelayanan kami.',
        icon: <DocumentTextIcon className="w-6 h-6" />,
        action: 'link',
        link: '/maklumat.pdf',
        category: 'Dokumen'
    },
    {
        title: 'Formulir Permohonan',
        description: 'Isi formulir permohonan informasi secara online.',
        icon: <ClipboardDocumentListIcon className="w-6 h-6" />,
        action: 'link',
        link: '/permohonan',
        category: 'Formulir'
    },
    {
        title: 'Formulir Keberatan',
        description: 'Unduh formulir keberatan informasi.',
        icon: <DocumentDuplicateIcon className="w-6 h-6" />,
        action: 'link',
        link: '/formulir-keberatan.pdf',
        category: 'Formulir'
    },
    {
        title: '<PERSON><PERSON><PERSON>',
        description: 'Lihat laporan layanan informasi.',
        icon: <DocumentCheckIcon className="w-6 h-6" />,
        action: 'link',
        link: '/laporan-layanan.pdf',
        category: 'Dokumen'
    },
    {
        title: 'FAQ',
        description: 'Pertanyaan yang sering diajukan.',
        icon: <QuestionMarkCircleIcon className="w-6 h-6" />,
        action: 'modal',
        modalContent: {
            title: 'Pertanyaan yang Sering Diajukan',
            content: 'Konten FAQ akan ditampilkan di sini.'
        },
        category: 'Informasi'
    },
    {
        title: 'Kontak Kami',
        description: 'Hubungi kami untuk informasi lebih lanjut.',
        icon: <ChatBubbleLeftRightIcon className="w-6 h-6" />,
        action: 'modal',
        modalContent: {
            title: 'Kontak Kami',
            content: 'Informasi kontak akan ditampilkan di sini.'
        },
        category: 'Informasi'
    }
]
