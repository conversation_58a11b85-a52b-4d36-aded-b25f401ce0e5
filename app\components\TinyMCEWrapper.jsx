'use client';

import { useState, useEffect, useRef } from 'react';
import { Editor } from '@tinymce/tinymce-react';

export default function TinyMCEWrapper({ data = '', onChange, config = {} }) {
  const editorRef = useRef(null);
  const [isEditorReady, setIsEditorReady] = useState(false);
  
  // Default height
  const editorHeight = config.height || 700;

  return (
    <div className="main-container">
      <div 
        className="editor-container"
        style={{
          border: '1px solid #D1D5DB',
          borderRadius: '0.375rem',
          overflow: 'hidden'
        }}
      >
        <Editor
          apiKey={process.env.NEXT_PUBLIC_TINYMCE_API_KEY || ''}
          onInit={(evt, editor) => {
            editorRef.current = editor;
            setIsEditorReady(true);
          }}
          initialValue={data}
          init={{
            height: editorHeight,
            menubar: true,
            plugins: [
              'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
              'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
              'insertdatetime', 'media', 'table', 'code', 'help', 'wordcount'
            ],
            toolbar: 'undo redo | blocks | ' +
              'bold italic forecolor | alignleft aligncenter ' +
              'alignright alignjustify | bullist numlist outdent indent | ' +
              'removeformat | help',
            content_style: 'body { font-family:Lato,sans-serif; font-size:16px }',
            language: 'id',
            placeholder: 'Ketik atau tempel konten Anda di sini!',
          }}
          onEditorChange={(content) => {
            onChange(content);
          }}
        />
      </div>
      
      {!isEditorReady && (
        <div className="absolute top-0 bottom-0 left-0 right-0 flex items-center justify-center p-4 border border-gray-300 rounded-md bg-gray-50" 
             style={{ minHeight: `${editorHeight}px`, width: '100%', zIndex: 10 }}>
          <div className="w-6 h-6 mr-2 border-2 border-t-2 border-gray-500 rounded-full border-t-primary-600 animate-spin"></div>
          <span className="text-sm text-gray-500">Loading editor...</span>
        </div>
      )}
    </div>
  );
}