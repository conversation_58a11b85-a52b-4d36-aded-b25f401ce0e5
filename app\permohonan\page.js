'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Textarea } from '../components/ui/textarea';
import { RadioGroup, RadioGroupItem } from '../components/ui/radio-group';
import { Alert, AlertDescription } from '../components/ui/alert';
import { FileText, Upload, CheckCircle, AlertCircle } from 'lucide-react';
import Nav from '../components/Nav';
import BuktiPengiriman from '../components/BuktiPengiriman';
import { useRouter } from 'next/navigation';

export default function PermohonanInformasiPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    tanggalPermohonan: new Date().toISOString().split('T')[0],
    kategoriPemohon: '',
    nik: '',
    namaSesuaiKtp: '',
    alamatLengkapSesuaiKtp: '',
    alamatTinggalSaatIni: '',
    nomorKontak: '',
    alamatEmail: '',
    pekerjaan: '',
    informasiYangDiminta: '',
    tujuanPermohonanInformasi: '',
    bentukInformasi: '',
    caraMendapatkanInformasi: ''
  });

  const [files, setFiles] = useState({
    formulirPermohonan: null,
    suratPernyataan: null,
    scanKtp: null
  });

  const [errors, setErrors] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(null);
  const [showSuccessModal, setShowSuccessModal] = useState(false);
  const [showBuktiPengiriman, setShowBuktiPengiriman] = useState(false);
  const [permohonanId, setPermohonanId] = useState(null);
  const [permohonanData, setPermohonanData] = useState(null);
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));

    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleFileChange = (e) => {
    const { name, files: fileList } = e.target;
    const file = fileList[0];

    if (file) {
      // Validate file size (max 1MB)
      if (file.size > 1024 * 1024) {
        setErrors(prev => ({
          ...prev,
          [name]: 'File terlalu besar. Maksimal 1MB.'
        }));
        return;
      }

      // Validate file type
      const allowedTypes = {
        formulirPermohonan: ['application/pdf'],
        suratPernyataan: ['application/pdf'],
        scanKtp: ['image/jpeg', 'image/jpg', 'image/png', 'image/gif']
      };

      if (!allowedTypes[name].includes(file.type)) {
        setErrors(prev => ({
          ...prev,
          [name]: 'Tipe file tidak diizinkan.'
        }));
        return;
      }

      setFiles(prev => ({
        ...prev,
        [name]: file
      }));

      // Clear error
      if (errors[name]) {
        setErrors(prev => ({
          ...prev,
          [name]: ''
        }));
      }
    }
  };

  const validateForm = () => {
    const newErrors = {};

    // Required fields validation
    const requiredFields = [
      'kategoriPemohon', 'nik', 'namaSesuaiKtp', 'alamatLengkapSesuaiKtp',
      'alamatTinggalSaatIni', 'nomorKontak', 'alamatEmail', 'pekerjaan',
      'informasiYangDiminta', 'tujuanPermohonanInformasi', 'bentukInformasi',
      'caraMendapatkanInformasi'
    ];

    requiredFields.forEach(field => {
      if (!formData[field] || formData[field].trim() === '') {
        newErrors[field] = 'Field ini wajib diisi';
      }
    });

    // Email validation
    if (formData.alamatEmail && !/\S+@\S+\.\S+/.test(formData.alamatEmail)) {
      newErrors.alamatEmail = 'Format email tidak valid';
    }

    // NIK validation (16 digits)
    if (formData.nik && !/^\d{16}$/.test(formData.nik)) {
      newErrors.nik = 'NIK harus 16 digit angka';
    }

    // Phone validation
    if (formData.nomorKontak && !/^[\d\-\+\(\)\s]+$/.test(formData.nomorKontak)) {
      newErrors.nomorKontak = 'Format nomor kontak tidak valid';
    }

    // File validation
    const requiredFiles = ['formulirPermohonan', 'suratPernyataan', 'scanKtp'];
    requiredFiles.forEach(fileField => {
      if (!files[fileField]) {
        newErrors[fileField] = 'File ini wajib diupload';
      }
    });

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!validateForm()) {
      setSubmitStatus({ type: 'error', message: 'Mohon lengkapi semua field yang wajib diisi' });
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      const submitFormData = new FormData();

      // Add form data
      Object.keys(formData).forEach(key => {
        submitFormData.append(key, formData[key]);
      });

      // Add files
      Object.keys(files).forEach(key => {
        if (files[key]) {
          submitFormData.append(key, files[key]);
        }
      });

      const response = await fetch('/api/permohonan/submit', {
        method: 'POST',
        body: submitFormData
      });

      if (response.ok) {
        const result = await response.json();
        setPermohonanId(result.id);

        // Store permohonan data for bukti pengiriman
        setPermohonanData({
          id: result.id,
          ...formData
        });

        // Show bukti pengiriman instead of success modal
        setShowBuktiPengiriman(true);

        // Reset form
        setFormData({
          tanggalPermohonan: new Date().toISOString().split('T')[0],
          kategoriPemohon: '',
          nik: '',
          namaSesuaiKtp: '',
          alamatLengkapSesuaiKtp: '',
          alamatTinggalSaatIni: '',
          nomorKontak: '',
          alamatEmail: '',
          pekerjaan: '',
          informasiYangDiminta: '',
          tujuanPermohonanInformasi: '',
          bentukInformasi: '',
          caraMendapatkanInformasi: ''
        });
        setFiles({
          formulirPermohonan: null,
          suratPernyataan: null,
          scanKtp: null
        });
      } else {
        const error = await response.json();
        setSubmitStatus({
          type: 'error',
          message: error.error || 'Terjadi kesalahan saat mengirim permohonan'
        });
      }
    } catch (error) {
      console.error('Submit error:', error);
      setSubmitStatus({
        type: 'error',
        message: 'Terjadi kesalahan saat mengirim permohonan'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleRedirectToHome = () => {
    setShowSuccessModal(false);
    router.push('/');
  };

  const handleCloseBuktiPengiriman = () => {
    setShowBuktiPengiriman(false);
    // Show success modal after closing bukti pengiriman
    setShowSuccessModal(true);

    // Auto redirect after 3 seconds
    setTimeout(() => {
      router.push('/');
    }, 3000);
  };

  const handleDownloadComplete = () => {
    // Optional: Show a toast or notification that download is complete
    console.log('Bukti pengiriman berhasil diunduh');
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Navigation */}
      <Nav />

      {/* Bukti Pengiriman Modal */}
      {showBuktiPengiriman && permohonanData && (
        <BuktiPengiriman
          permohonanData={permohonanData}
          onClose={handleCloseBuktiPengiriman}
          onDownloadComplete={handleDownloadComplete}
        />
      )}

      {/* Success Modal */}
      {showSuccessModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
            <div className="text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-4">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>

              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Permohonan Berhasil Dikirim!
              </h3>

              <p className="text-sm text-gray-500 mb-4">
                Permohonan informasi Anda telah berhasil disimpan dengan ID:
              </p>

              <div className="bg-gray-50 rounded-md p-3 mb-4">
                <code className="text-sm font-mono text-blue-600">
                  {permohonanId}
                </code>
              </div>

              <p className="text-sm text-gray-500 mb-6">
                Anda akan diarahkan ke beranda dalam beberapa detik, atau klik tombol di bawah.
              </p>

              <div className="flex gap-3 justify-center">
                <Button
                  onClick={handleRedirectToHome}
                  className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2"
                >
                  Kembali ke Beranda
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="px-4 mx-auto max-w-4xl sm:px-6 lg:px-8 pt-20 pb-8">
        {/* Header */}
        <div className="p-6 mb-6 bg-white border rounded-lg shadow-sm">
          <h1 className="mb-2 text-2xl font-bold text-blue-800">
            Formulir Permohonan Informasi PPID BPMP
          </h1>
          <p className="text-gray-600">
            Balai Penjaminan Mutu Pendidikan Provinsi Kalimantan Timur
          </p>
        </div>

        {/* Status Alert */}
        {submitStatus && (
          <Alert className={`mb-6 ${submitStatus.type === 'success' ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}`}>
            <div className="flex items-center">
              {submitStatus.type === 'success' ? (
                <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-600 mr-2" />
              )}
              <AlertDescription className={submitStatus.type === 'success' ? 'text-green-800' : 'text-red-800'}>
                {submitStatus.message}
              </AlertDescription>
            </div>
          </Alert>
        )}

        {/* Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Data Pemohon */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Data Pemohon
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="tanggalPermohonan">Tanggal Permohonan *</Label>
                  <Input
                    id="tanggalPermohonan"
                    name="tanggalPermohonan"
                    type="date"
                    value={formData.tanggalPermohonan}
                    onChange={handleInputChange}
                    className={errors.tanggalPermohonan ? 'border-red-500' : ''}
                  />
                  {errors.tanggalPermohonan && (
                    <p className="text-sm text-red-600 mt-1">{errors.tanggalPermohonan}</p>
                  )}
                </div>

                <div>
                  <Label>Kategori Pemohon *</Label>
                  <RadioGroup
                    value={formData.kategoriPemohon}
                    onValueChange={(value) => handleInputChange({ target: { name: 'kategoriPemohon', value } })}
                    className="mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Perorangan" id="perorangan" />
                      <Label htmlFor="perorangan">Perorangan</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Kelompok Orang" id="kelompok" />
                      <Label htmlFor="kelompok">Kelompok Orang</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Badan Hukum" id="badan-hukum" />
                      <Label htmlFor="badan-hukum">Badan Hukum</Label>
                    </div>
                  </RadioGroup>
                  {errors.kategoriPemohon && (
                    <p className="text-sm text-red-600 mt-1">{errors.kategoriPemohon}</p>
                  )}
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="nik">NIK *</Label>
                  <Input
                    id="nik"
                    name="nik"
                    value={formData.nik}
                    onChange={handleInputChange}
                    placeholder="16 digit NIK"
                    maxLength={16}
                    className={errors.nik ? 'border-red-500' : ''}
                  />
                  {errors.nik && (
                    <p className="text-sm text-red-600 mt-1">{errors.nik}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="namaSesuaiKtp">Nama Sesuai KTP *</Label>
                  <Input
                    id="namaSesuaiKtp"
                    name="namaSesuaiKtp"
                    value={formData.namaSesuaiKtp}
                    onChange={handleInputChange}
                    className={errors.namaSesuaiKtp ? 'border-red-500' : ''}
                  />
                  {errors.namaSesuaiKtp && (
                    <p className="text-sm text-red-600 mt-1">{errors.namaSesuaiKtp}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="alamatLengkapSesuaiKtp">Alamat Lengkap Sesuai KTP *</Label>
                <Textarea
                  id="alamatLengkapSesuaiKtp"
                  name="alamatLengkapSesuaiKtp"
                  value={formData.alamatLengkapSesuaiKtp}
                  onChange={handleInputChange}
                  rows={3}
                  className={errors.alamatLengkapSesuaiKtp ? 'border-red-500' : ''}
                />
                {errors.alamatLengkapSesuaiKtp && (
                  <p className="text-sm text-red-600 mt-1">{errors.alamatLengkapSesuaiKtp}</p>
                )}
              </div>

              <div>
                <Label htmlFor="alamatTinggalSaatIni">Alamat Tinggal Saat Ini *</Label>
                <Textarea
                  id="alamatTinggalSaatIni"
                  name="alamatTinggalSaatIni"
                  value={formData.alamatTinggalSaatIni}
                  onChange={handleInputChange}
                  rows={3}
                  className={errors.alamatTinggalSaatIni ? 'border-red-500' : ''}
                />
                {errors.alamatTinggalSaatIni && (
                  <p className="text-sm text-red-600 mt-1">{errors.alamatTinggalSaatIni}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="nomorKontak">Nomor Kontak *</Label>
                  <Input
                    id="nomorKontak"
                    name="nomorKontak"
                    value={formData.nomorKontak}
                    onChange={handleInputChange}
                    placeholder="08xxxxxxxxxx"
                    className={errors.nomorKontak ? 'border-red-500' : ''}
                  />
                  {errors.nomorKontak && (
                    <p className="text-sm text-red-600 mt-1">{errors.nomorKontak}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="alamatEmail">Alamat Email *</Label>
                  <Input
                    id="alamatEmail"
                    name="alamatEmail"
                    type="email"
                    value={formData.alamatEmail}
                    onChange={handleInputChange}
                    placeholder="<EMAIL>"
                    className={errors.alamatEmail ? 'border-red-500' : ''}
                  />
                  {errors.alamatEmail && (
                    <p className="text-sm text-red-600 mt-1">{errors.alamatEmail}</p>
                  )}
                </div>
              </div>

              <div>
                <Label htmlFor="pekerjaan">Pekerjaan *</Label>
                <Input
                  id="pekerjaan"
                  name="pekerjaan"
                  value={formData.pekerjaan}
                  onChange={handleInputChange}
                  className={errors.pekerjaan ? 'border-red-500' : ''}
                />
                {errors.pekerjaan && (
                  <p className="text-sm text-red-600 mt-1">{errors.pekerjaan}</p>
                )}
              </div>
            </CardContent>
          </Card>
          {/* Informasi yang Diminta */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Informasi yang Diminta
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="informasiYangDiminta">Informasi yang Diminta *</Label>
                <Textarea
                  id="informasiYangDiminta"
                  name="informasiYangDiminta"
                  value={formData.informasiYangDiminta}
                  onChange={handleInputChange}
                  rows={4}
                  placeholder="Jelaskan secara detail informasi yang Anda butuhkan..."
                  className={errors.informasiYangDiminta ? 'border-red-500' : ''}
                />
                {errors.informasiYangDiminta && (
                  <p className="text-sm text-red-600 mt-1">{errors.informasiYangDiminta}</p>
                )}
              </div>

              <div>
                <Label htmlFor="tujuanPermohonanInformasi">Tujuan Permohonan Informasi *</Label>
                <Textarea
                  id="tujuanPermohonanInformasi"
                  name="tujuanPermohonanInformasi"
                  value={formData.tujuanPermohonanInformasi}
                  onChange={handleInputChange}
                  rows={3}
                  placeholder="Jelaskan tujuan penggunaan informasi..."
                  className={errors.tujuanPermohonanInformasi ? 'border-red-500' : ''}
                />
                {errors.tujuanPermohonanInformasi && (
                  <p className="text-sm text-red-600 mt-1">{errors.tujuanPermohonanInformasi}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label>Bentuk Informasi *</Label>
                  <RadioGroup
                    value={formData.bentukInformasi}
                    onValueChange={(value) => handleInputChange({ target: { name: 'bentukInformasi', value } })}
                    className="mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Soft Copy" id="soft-copy" />
                      <Label htmlFor="soft-copy">Soft Copy</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Hard Copy" id="hard-copy" />
                      <Label htmlFor="hard-copy">Hard Copy</Label>
                    </div>
                  </RadioGroup>
                  {errors.bentukInformasi && (
                    <p className="text-sm text-red-600 mt-1">{errors.bentukInformasi}</p>
                  )}
                </div>

                <div>
                  <Label>Cara Mendapatkan Informasi *</Label>
                  <RadioGroup
                    value={formData.caraMendapatkanInformasi}
                    onValueChange={(value) => handleInputChange({ target: { name: 'caraMendapatkanInformasi', value } })}
                    className="mt-2"
                  >
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Email" id="email" />
                      <Label htmlFor="email">Email</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Pos" id="pos" />
                      <Label htmlFor="pos">Pos</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="Langsung" id="langsung" />
                      <Label htmlFor="langsung">Langsung</Label>
                    </div>
                  </RadioGroup>
                  {errors.caraMendapatkanInformasi && (
                    <p className="text-sm text-red-600 mt-1">{errors.caraMendapatkanInformasi}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upload File */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Upload Dokumen Pendukung
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="formulirPermohonan">Formulir Permohonan Informasi (PDF) *</Label>
                <Input
                  id="formulirPermohonan"
                  name="formulirPermohonan"
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                  className={errors.formulirPermohonan ? 'border-red-500' : ''}
                />
                <p className="text-sm text-gray-500 mt-1">Format: PDF, Maksimal 1MB</p>
                {errors.formulirPermohonan && (
                  <p className="text-sm text-red-600 mt-1">{errors.formulirPermohonan}</p>
                )}
                {files.formulirPermohonan && (
                  <p className="text-sm text-green-600 mt-1">✓ {files.formulirPermohonan.name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="suratPernyataan">Surat Pernyataan Bermaterai (PDF) *</Label>
                <Input
                  id="suratPernyataan"
                  name="suratPernyataan"
                  type="file"
                  accept=".pdf"
                  onChange={handleFileChange}
                  className={errors.suratPernyataan ? 'border-red-500' : ''}
                />
                <p className="text-sm text-gray-500 mt-1">Format: PDF, Maksimal 1MB</p>
                {errors.suratPernyataan && (
                  <p className="text-sm text-red-600 mt-1">{errors.suratPernyataan}</p>
                )}
                {files.suratPernyataan && (
                  <p className="text-sm text-green-600 mt-1">✓ {files.suratPernyataan.name}</p>
                )}
              </div>

              <div>
                <Label htmlFor="scanKtp">Scan/Foto KTP *</Label>
                <Input
                  id="scanKtp"
                  name="scanKtp"
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className={errors.scanKtp ? 'border-red-500' : ''}
                />
                <p className="text-sm text-gray-500 mt-1">Format: JPG, PNG, GIF, Maksimal 1MB</p>
                {errors.scanKtp && (
                  <p className="text-sm text-red-600 mt-1">{errors.scanKtp}</p>
                )}
                {files.scanKtp && (
                  <p className="text-sm text-green-600 mt-1">✓ {files.scanKtp.name}</p>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Submit Button */}
          <div className="flex justify-end">
            <Button
              type="submit"
              disabled={isSubmitting}
              className="px-8 py-2 bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isSubmitting ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Mengirim...
                </>
              ) : (
                'Kirim Permohonan'
              )}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}
