// Enhanced and secure Tags API route
import { NextResponse } from 'next/server';
import { prisma } from '../../../lib/prisma';
import { cookies } from 'next/headers';
import { verifyToken } from '../../lib/auth';
import { 
  validateTagInput, 
  generateSlug, 
  checkRateLimit,
  sanitizeInput 
} from '../../lib/validation';

// GET untuk mendapatkan semua tag dengan caching headers
export async function GET() {
  try {
    // Cek apakah tag default sudah ada
    const defaultTags = [
      { name: 'Informasi Berkala', slug: 'informasi-berkala' },
      { name: 'Informasi Serta Merta', slug: 'informasi-serta-merta' },
      { name: 'Informasi Setiap Saat', slug: 'informasi-setiap-saat' },
      { name: 'Informasi Dikecualikan', slug: 'informasi-dikecualikan' }
    ];

    // Buat tag default jika belum ada (hanya sekali)
    const tagCount = await prisma.tag.count();
    if (tagCount === 0) {
      await prisma.$transaction(async (tx) => {
        for (const tag of defaultTags) {
          await tx.tag.create({
            data: {
              name: tag.name,
              slug: tag.slug,
              description: `Kategori untuk ${tag.name.toLowerCase()}`,
              createdAt: new Date(),
              updatedAt: new Date()
            }
          });
        }
      });
    }

    // Ambil semua tag dengan optimasi query
    const tags = await prisma.tag.findMany({
      orderBy: {
        name: 'asc'
      },
      select: {
        id: true,
        name: true,
        slug: true,
        description: true,
        createdAt: true,
        updatedAt: true,
        _count: {
          select: {
            posts: true // Hitung jumlah post yang menggunakan tag ini
          }
        }
      }
    });
    
    const response = NextResponse.json({ 
      tags,
      total: tags.length,
      timestamp: new Date().toISOString()
    });
    
    // Set cache headers for better performance
    response.headers.set('Cache-Control', 'public, max-age=300, stale-while-revalidate=60');
    response.headers.set('ETag', `"${Date.now()}"`);
    
    return response;
  } catch (error) {
    console.error('Error fetching tags:', error);
    return NextResponse.json(
      { error: 'Terjadi kesalahan saat mengambil data tag' },
      { status: 500 }
    );
  }
}

// POST untuk membuat tag baru dengan enhanced security
export async function POST(request) {
  try {
    // Rate limiting check
    const clientIP = request.headers.get('x-forwarded-for') || 
                    request.headers.get('x-real-ip') || 
                    request.ip || 'unknown';
                    
    if (!checkRateLimit(clientIP, 5, 60000)) { // 5 requests per minute
      return NextResponse.json(
        { error: 'Terlalu banyak permintaan. Silakan coba lagi dalam 1 menit.' },
        { status: 429 }
      );
    }

    // Authentication check
    const cookieStore = await cookies();
    const token = cookieStore.get('token')?.value;
    
    if (!token) {
      return NextResponse.json(
        { error: 'Akses ditolak. Silakan login terlebih dahulu.' },
        { status: 401 }
      );
    }
    
    // Verify token and get user data
    const userData = await verifyToken(token);
    if (!userData || !userData.id) {
      return NextResponse.json(
        { error: 'Token tidak valid atau telah kadaluwarsa' },
        { status: 401 }
      );
    }

    // Authorization check - only admins can create tags
    if (!userData.role || userData.role.toLowerCase() !== 'admin') {
      return NextResponse.json(
        { error: 'Anda tidak memiliki izin untuk membuat tag' },
        { status: 403 }
      );
    }
    
    // Parse and validate request body
    let body;
    try {
      body = await request.json();
    } catch (error) {
      return NextResponse.json(
        { error: 'Format data tidak valid' },
        { status: 400 }
      );
    }

    // Validate and sanitize input
    const validation = validateTagInput(body);
    if (!validation.success) {
      return NextResponse.json(
        { 
          error: 'Data tidak valid', 
          details: validation.errors 
        },
        { status: 400 }
      );
    }

    const { name, description } = validation.data;

    // Generate slug dari nama yang sudah disanitasi
    const slug = generateSlug(name);

    // Check for existing tags (case-insensitive)
    const existingTag = await prisma.tag.findFirst({
      where: {
        OR: [
          { name: { equals: name, mode: 'insensitive' } },
          { slug: slug }
        ]
      }
    });
    
    if (existingTag) {
      return NextResponse.json(
        { error: 'Tag dengan nama atau slug tersebut sudah ada' },
        { status: 409 } // Conflict status
      );
    }
    
    // Create new tag with transaction for data integrity
    const newTag = await prisma.$transaction(async (tx) => {
      return await tx.tag.create({
        data: {
          name,
          slug,
          description: description || null,
          createdAt: new Date(),
          updatedAt: new Date()
        },
        select: {
          id: true,
          name: true,
          slug: true,
          description: true,
          createdAt: true,
          updatedAt: true
        }
      });
    });

    // Log the successful action
    console.log(`Tag created: "${name}" (${slug}) by user ${userData.username || userData.id}`);
    
    return NextResponse.json({ 
      message: 'Tag berhasil dibuat',
      tag: newTag 
    }, { status: 201 });
    
  } catch (error) {
    console.error('Error creating tag:', error);
    
    // Don't expose internal error details to client
    if (error.code === 'P2002') { // Prisma unique constraint error
      return NextResponse.json(
        { error: 'Tag dengan nama tersebut sudah ada' },
        { status: 409 }
      );
    }
    
    return NextResponse.json(
      { error: 'Terjadi kesalahan internal server' },
      { status: 500 }
    );
  }
}
