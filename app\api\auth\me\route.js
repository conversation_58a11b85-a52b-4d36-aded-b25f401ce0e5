import { NextResponse } from 'next/server';
import { verifyToken } from '../../lib/auth';
import { prisma } from '../../lib/prisma';

export async function GET(request) {
  try {
    // Try to get token from cookie first, then fallback to Authorization header
    let token = request.cookies.get('token')?.value;
    
    if (!token) {
      const authHeader = request.headers.get('authorization');
      if (authHeader && authHeader.startsWith('Bearer ')) {
        token = authHeader.split(' ')[1];
      }
    }

    if (!token) {
      return NextResponse.json(
        { message: 'Token tidak ditemukan' },
        { status: 401 }
      );
    }

    const payload = await verifyToken(token);

    if (!payload) {
      return NextResponse.json(
        { message: 'Token tidak valid' },
        { status: 401 }
      );
    }

    // Get user data from database
    const user = await prisma.user.findUnique({
      where: { id: payload.id },
      select: {
        id: true,
        username: true,
        email: true,
        role: true,
        createdAt: true,
      },
    });    if (!user) {
      return NextResponse.json(
        { message: 'User tidak ditemukan' },
        { status: 404 }
      );
    }

    // Normalize user role to lowercase for consistent permissions checking
    if (user.role) {
      user.role = user.role.toLowerCase();
    }
    
    console.log('User data from /api/auth/me:', { 
      id: user.id, 
      username: user.username, 
      role: user.role 
    });

    return NextResponse.json({
      user,
    });
  } catch (error) {
    console.error('Auth me error:', error);
    return NextResponse.json(
      { message: 'Terjadi kesalahan server' },
      { status: 500 }
    );
  }
}
